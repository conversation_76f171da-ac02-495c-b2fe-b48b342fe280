#!/usr/bin/env python3
"""
测试 plot_anns 函数修复
"""

import numpy as np
import torch
import sys
import os

# 添加当前目录到路径
sys.path.append('.')

def create_test_data():
    """创建测试数据"""
    
    # 创建测试图像
    h, w = 480, 640
    image = np.random.randint(0, 255, (h, w, 3), dtype=np.uint8)
    
    # 创建测试masks
    n_masks = 3
    masks = torch.zeros((n_masks, h, w), dtype=torch.bool)
    
    # Mask 1: 左上角
    masks[0, 50:150, 50:150] = True
    
    # Mask 2: 右上角
    masks[1, 50:150, 400:500] = True
    
    # Mask 3: 中心
    masks[2, 200:300, 250:350] = True
    
    # 创建其他数据
    scores = torch.tensor([0.9, 0.8, 0.7])
    target_bboxes = torch.tensor([
        [50, 50, 150, 150],
        [400, 50, 500, 150], 
        [250, 200, 350, 300]
    ], dtype=torch.float32)
    obj_labels = ['car', 'truck', 'bus']
    
    return image, masks, scores, target_bboxes, obj_labels

def test_plot_anns():
    """测试 plot_anns 函数"""
    
    from Grouding_SAM_Utils import plot_anns, pack_anno
    
    print("测试 plot_anns 函数...")
    
    # 创建测试数据
    image, masks, scores, target_bboxes, obj_labels = create_test_data()
    
    print(f"测试数据:")
    print(f"  image shape: {image.shape}")
    print(f"  masks shape: {masks.shape}")
    print(f"  scores: {scores}")
    print(f"  target_bboxes shape: {target_bboxes.shape}")
    print(f"  obj_labels: {obj_labels}")
    
    try:
        # 打包annotations
        anns = pack_anno(masks, scores, target_bboxes, obj_labels)
        print(f"\nAnnotations:")
        for i, ann in enumerate(anns):
            print(f"  Ann {i}: segmentation shape: {ann['segmentation'].shape}, area: {ann['area']}")
        
        # 调用 plot_anns
        result = plot_anns(anns, image)
        
        print(f"\n结果:")
        print(f"  result type: {type(result)}")
        print(f"  result shape: {result.shape}")
        print(f"  result dtype: {result.dtype}")
        
        # 验证结果
        assert isinstance(result, np.ndarray), f"Expected numpy array, got {type(result)}"
        assert len(result.shape) == 3, f"Expected 3D array, got shape {result.shape}"
        assert result.shape[2] in [3, 4], f"Expected RGB or RGBA image, got shape {result.shape}"
        
        print("✅ plot_anns 测试通过！")
        
        # 保存结果
        os.makedirs("debug_output", exist_ok=True)
        import matplotlib.pyplot as plt
        plt.imsave("debug_output/test_plot_anns_result.png", result)
        print("✅ 结果图像已保存到 debug_output/test_plot_anns_result.png")
        
        return True
        
    except Exception as e:
        print(f"❌ plot_anns 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_size_mismatch():
    """测试尺寸不匹配的情况"""
    
    from Grouding_SAM_Utils import plot_anns, pack_anno
    
    print("\n测试尺寸不匹配的情况...")
    
    # 创建不同尺寸的图像和mask
    image = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
    
    # 创建不同尺寸的mask
    masks = torch.zeros((1, 240, 320), dtype=torch.bool)  # 一半尺寸
    masks[0, 50:100, 50:100] = True
    
    scores = torch.tensor([0.9])
    target_bboxes = torch.tensor([[50, 50, 100, 100]], dtype=torch.float32)
    obj_labels = ['car']
    
    print(f"图像尺寸: {image.shape}")
    print(f"Mask尺寸: {masks.shape}")
    
    try:
        anns = pack_anno(masks, scores, target_bboxes, obj_labels)
        result = plot_anns(anns, image)
        
        print(f"✅ 尺寸不匹配测试通过: {result.shape}")
        return True
        
    except Exception as e:
        print(f"❌ 尺寸不匹配测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success1 = test_plot_anns()
    success2 = test_size_mismatch()
    
    if success1 and success2:
        print("\n🎉 所有测试通过！plot_anns 函数修复成功。")
    else:
        print("\n💥 部分测试失败，需要进一步调试。")
