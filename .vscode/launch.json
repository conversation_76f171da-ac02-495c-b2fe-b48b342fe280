{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        

        {
            "name": "Python: Current File",
            "type": "debugpy",
            "request": "launch",
            "program": "${file}",
            "console": "integratedTerminal",
            "python": "/home/<USER>/anaconda3/envs/p3former/bin/python",
            "justMyCode": true,
            "env":{
                "CUDA_VISIBLE_DEVICES":"3"
            }
        },

        {
            "name": "Python: setup",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/mmcv/setup.py",
            "cwd": "${workspaceFolder}/mmcv",
            "console": "integratedTerminal",
            "python": "/home/<USER>/anaconda3/envs/3dmm/bin/python",
            "justMyCode": false,
            "args": [
                "-v",
                "-e",
                "."
                ],
        },
        {
            "name": "Python: point+p3former",
            "type": "python",
            "request": "launch",
            "program": "${workspaceFolder}/train.py",
            "cwd": "${workspaceFolder}",
            "console": "integratedTerminal",
            "justMyCode": false,
            "args": [
                "${workspaceFolder}/configs/spg/point_pointnet2msg_p3former_1xb2_semantickitti_train_13w_aug_valtop12.py"
            ],
            "python": "/home/<USER>/anaconda3/envs/p3former3/bin/python",
            "env":{
                "CUDA_VISIBLE_DEVICES":"3"
        }
        },
        {
            "name": "Python: dino",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/Grouding_SAM_Main_UDA.py",
            "cwd": "${workspaceFolder}",
            "console": "integratedTerminal",
            "justMyCode": false,
            "args": [
                "--data_mode", "mini",
                "--dset", "val",
                "--part", "1/1",
            ],
            "python": "/mnt/data/users/yining.pan/anaconda3/envs/dino/bin/python",
            "env":{
                "CUDA_LAUNCH_BLOCKING":"1",
                "CUDA_VISIBLE_DEVICES":"3"
        }
        },
        {
            "name": "Python: nusc+p3former",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/train.py",
            "cwd": "${workspaceFolder}",
            "console": "integratedTerminal",
            "justMyCode": false,
            "args": [
                "${workspaceFolder}/configs/UDA/meanteacher/IAL_MT_debug_light_iter_XsyncBuffer_insert-3_PLgrowSG_hmPL_codeJune_syncWrap_shareQ_consist.py",
                // "${workspaceFolder}/work_dirs/IAL_MT/epoch_1.pth"
                // "${workspaceFolder}/configs/nuscenes/p3former_1xb2_nuscenes_debug_hard.py"
                // "${workspaceFolder}/configs/nuscenes/image/mask2former_nusc.py"
            ],
            "python": "/home/<USER>/anaconda3/envs/p3former/bin/python",
            "env":{
                "CUDA_VISIBLE_DEVICES":"0",
                "CUDA_LAUNCH_BLOCKING":"1"
        }
        },
        {
                "name": "Python: torchrun (DDP)",
                "type": "debugpy",
                "request": "launch",
                "program": "/home/<USER>/anaconda3/envs/p3former/lib/python3.8/site-packages/torch/distributed/run.py",  // DDP
                "console": "integratedTerminal",
                "cwd": "${workspaceFolder}",
                "args": [
                  "--nproc_per_node=2",
                  "--nnodes=1",
                  "--node_rank=0",
                  "--master_addr=127.0.0.1",
                  "--master_port=29500",
                  "${workspaceFolder}/train.py",
                  "${workspaceFolder}/configs/UDA/meanteacher/July/IAL_MT_city_syncBuffer_PLgrowSGst_XshareQ_Xconsist_HPC4.py",
                  "--launcher", "pytorch"
                ],
                "python": "/home/<USER>/anaconda3/envs/p3former/bin/python",
                "env": {
                "CUDA_LAUNCH_BLOCKING": "1",
                  "CUDA_VISIBLE_DEVICES": "0,1"
                },
                "justMyCode": false
          },
        {
            "name": "Python: spv+p3former",
            "type": "python",
            "request": "launch",
            "program": "${workspaceFolder}/train.py",
            "cwd": "${workspaceFolder}",
            "console": "integratedTerminal",
            "justMyCode": false,
            "args": [
                "${workspaceFolder}/configs/spg/spg_cylinder_p3former_1xb2_semantickitti_debug.py"
            ],
            "python": "/home/<USER>/anaconda3/envs/p3former3/bin/python",
            "env":{
                "CUDA_VISIBLE_DEVICES":"3"
        }
        },
        {
            "name": "Python: visualize",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/test.py",
            "cwd": "${workspaceFolder}",
            "console": "integratedTerminal",
            "justMyCode": false,
            "args": [
                // "${workspaceFolder}/configs/nuscenes/p3former_1xb2_nuscenes_test_soft_seghead.py",
                // "${workspaceFolder}/configs/submit/nuscenes_submit_single_2024_HPC4.py",
                // "${workspaceFolder}/work_dirs/nuscenes_trainval_single_2024_HPC4/epoch_87.pth"
                "${workspaceFolder}/configs/UDA/meanteacher/IAL_MT_debug_light_iter_XsyncBuffer_insert-3_PLgrowGTsi_hmPL_codeJune_syncWrap_XshareQ_Xconsist.py",
                "${workspaceFolder}/work_dirs/IAL_light_source-only_HPC3/epoch_68.pth"

            ],
            "python": "/home/<USER>/anaconda3/envs/p3former/bin/python",
            "env":{
                "CUDA_VISIBLE_DEVICES":"0",
                "CUDA_LAUNCH_BLOCKING":"1"
        }
        },
        {
            "name": "Python: curve_plot",
            "type": "python",
            "request": "launch",
            "program": "${workspaceFolder}/tools/analysis_tools/analyze_logs.py",
            "cwd": "${workspaceFolder}",
            "console": "integratedTerminal",
            "justMyCode": false,
            "args": [
                "plot_curve",
                "${workspaceFolder}/tools/analysis_tools/test_demo.json",
                "--keys","loss",
                "--title","loss",
                "--out","mm_hard_loss.png"
                ],
            "python": "/home/<USER>/anaconda3/envs/p3former/bin/python",

        },
    ]
}