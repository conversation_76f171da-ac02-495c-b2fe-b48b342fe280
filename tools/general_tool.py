import os
import re
import torch
import torch.nn.functional as F
import numpy as np
from skimage.transform import resize
from skimage.measure import label

# from tools.projection.pc2img import cartesian2polar_torch
############################################
# load data from pkl file, a simple way to achieve dataloader

def lift_instance_mask(instance_mask, include_background=False):
    """
    将 [H, W] 的 instance mask 提升为 [N, H, W] 的 binary mask。

    Args:
        instance_mask (ndarray): [H, W] 的 numpy array，每个像素为 instance id。
        include_background (bool): 是否包含背景（id==0）。

    Returns:
        binary_masks (ndarray): [N, H, W] 的 binary array，第 i 个 channel 对应第 i 个 instance。
        instance_ids (ndarray): [N] 的 array，对应每个 channel 的 instance id。
    """
    unique_ids = np.unique(instance_mask)
    if not include_background:
        unique_ids = unique_ids[unique_ids != 0]

    binary_masks = (instance_mask[None, :, :] == unique_ids[:, None, None]).astype(np.uint8)
    return binary_masks, unique_ids

def mask_to_bbox_mask(instance_mask):
    """
    将instance mask转换为bbox mask。

    参数:
        instance_mask (np.ndarray): 输入的h*w mask，背景为0，不同instance用不同正整数标记。

    返回:
        bbox_mask (np.ndarray): h*w数组，每个实例区域被其bbox覆盖，值与原instance ID一致。
    """
    h, w = instance_mask.shape
    bbox_mask = np.zeros((h, w), dtype=instance_mask.dtype)
    instance_ids = np.unique(instance_mask)
    instance_ids = instance_ids[instance_ids != 0]  # 去掉背景

    for inst_id in instance_ids:
        y_indices, x_indices = np.where(instance_mask == inst_id)
        if len(y_indices) == 0 or len(x_indices) == 0:
            continue  # 跳过空mask
        y_min, y_max = y_indices.min(), y_indices.max()
        x_min, x_max = x_indices.min(), x_indices.max()
        bbox_mask[y_min:y_max+1, x_min:x_max+1] = inst_id

    return bbox_mask

def load_point(pts_filename, backend_args=None, load_dim=5, use_dim=range(3)):
    try:
        from mmengine.fileio import get
        pts_bytes = get(pts_filename, backend_args=backend_args)
        points = np.frombuffer(pts_bytes, dtype=np.float32)
    except:
        points = np.fromfile(pts_filename, dtype=np.float32)
    points = points.reshape(-1, load_dim)
    points = points[:, use_dim]
    return points

def load_mask(pts_panoptic_mask_path, backend_args=None, seg_3d_dtype=np.int64, seg_offset=1000):
    pts_panoptic_mask = np.load(pts_panoptic_mask_path) 
    
    pts_panoptic_mask = pts_panoptic_mask['data']
    pts_semantic_mask = pts_panoptic_mask // seg_offset
    pts_instance_mask = pts_panoptic_mask.astype(seg_3d_dtype)
    return pts_semantic_mask, pts_instance_mask

def load_viewimages(filenames, backend_args=None, color_type='unchanged'):
    VIEWS = ['CAM_FRONT', 'CAM_FRONT_RIGHT', 'CAM_FRONT_LEFT', 'CAM_BACK', 'CAM_BACK_LEFT', 'CAM_BACK_RIGHT']

    imgs = {}
    for view in VIEWS:
        try:
            from mmengine.fileio import get
            import mmcv
            img_byte = get(filenames[view], backend_args=backend_args)
            img = mmcv.imfrombytes(img_byte, flag=color_type) 
            imgs[view] = img
        except:
            from PIL import Image
            img = Image.open(filenames[view]).convert("RGB")
            img = np.asarray(img)
            imgs[view] = img
    return imgs

def load_viewimages_meta(filenames, backend_args=None, color_type='unchanged'):
    VIEWS = ['CAM_FRONT', 'CAM_FRONT_RIGHT', 'CAM_FRONT_LEFT', 'CAM_BACK', 'CAM_BACK_LEFT', 'CAM_BACK_RIGHT']

    imgs = {}
    for view in VIEWS:
        imgs[view] = {}
        # load image 
        img_byte = get(filenames[view], backend_args=backend_args)
        img = mmcv.imfrombytes(img_byte, flag=color_type) 
        imgs[view]['img'] = img
        # load image meta
        
    return imgs

def load_point_mask_viewimages(pklfile, data_root, clamp_points=False, point_cloud_range=None):
    label_map = {
            1: 0, 5: 0, 7: 0, 8: 0, 10: 0, 11: 0, 13: 0, 19: 0, 20: 0, 0: 0, 29: 0, 31: 0,
            9: 1, 14: 2, 15: 3, 16: 3, 17: 4, 18: 5, 21: 6, 2: 7, 3: 7, 4: 7, 6: 7, 12: 8,
            22: 9, 23: 10, 24: 11, 25: 12, 26: 13, 27: 14, 28: 15, 30: 16
            }
    
    info_list = pklfile['data_list']
    VIEWS = ['CAM_FRONT', 'CAM_FRONT_RIGHT', 'CAM_FRONT_LEFT', 'CAM_BACK', 'CAM_BACK_LEFT', 'CAM_BACK_RIGHT']

    data_prefix = {
        'dataset_path': data_root,
        'lidar_path': data_root+'/samples/LIDAR_TOP',
        'pts_panoptic_mask': data_root+'/panoptic/v1.0-mini',
        'CAM_FRONT': data_root+'/samples/CAM_FRONT',
        'CAM_FRONT_RIGHT': data_root+'/samples/CAM_FRONT_RIGHT',
        'CAM_FRONT_LEFT': data_root+'/samples/CAM_FRONT_LEFT',
        'CAM_BACK': data_root+'/samples/CAM_BACK',
        'CAM_BACK_LEFT': data_root+'/samples/CAM_BACK_LEFT',
        'CAM_BACK_RIGHT': data_root+'/samples/CAM_BACK_RIGHT',}

    res_list = []
    for info in info_list:
        res = {}
        
        # load lidar points
        lidar_prefix = data_prefix.get('lidar_path')
        lidar_path = info['lidar_points']['lidar_path']
        lidar_path = os.path.join(lidar_prefix, lidar_path)
        lidar_point = load_point(lidar_path)
        # if clamp_points and point_cloud_range is not None:
            # clamp points to a range, not clip
            # lidar_point = lidar_point[(lidar_point[:, 0] >= point_cloud_range[0]) & (lidar_point[:, 0] < point_cloud_range[3])]
        res['lidar_point'] = lidar_point
        res['lidar_path'] = lidar_path
        
        # load panoptic mask
        dataset_path = data_prefix.get('dataset_path')
        panoptic_path = info['pts_panoptic_mask_path']
        panoptic_path = os.path.join(dataset_path, panoptic_path)
        pts_semantic_mask, pts_instance_mask = load_mask(panoptic_path)
        # map to 0-19 classes
        pts_semantic_mask = np.vectorize(label_map.get)(pts_semantic_mask)
        res['pts_semantic_mask'] = pts_semantic_mask
        res['pts_instance_mask'] = pts_instance_mask
        
        # load multi-view images
        img_files_path = {}
        for cam_id, img_info in info['images'].items(): # add prefix to img_path
            if 'img_path' in img_info:
                if cam_id in data_prefix:
                    cam_prefix = data_prefix[cam_id]
                img_info['img_path'] = os.path.join(cam_prefix,
                                                img_info['img_path'])
                img_files_path[cam_id] = img_info['img_path']
        imgs = load_viewimages(img_files_path)
        
        # pack by view
        meta_info = {}
        meta_info['img'] = {}
        
        for view in VIEWS:
            meta_info['img'][view] = imgs[view]
            
        # load multi-view images meta
        meta_info['lidar2cam'], meta_info['cam2img'] = {}, {}
        for cam_id, img_info in info['images'].items(): # add prefix to img_path
            # meta_info[cam_id] = {}
            if 'lidar2cam' in img_info:
                meta_info['lidar2cam'][cam_id] = np.array(img_info['lidar2cam'])
            if 'cam2img' in img_info:
                meta_info['cam2img'][cam_id] = np.array(img_info['cam2img'])

        res['imgs_meta'] = meta_info
        
        # load token
        sample_token = info['token']
        #TODO-pan: add token to res
        res['sample_token'] = sample_token
        res_list.append(res)
    return res_list############################################

def get_subtask_name(path, dir_name='/samples_slic/'):
    """Get the subtask name from the path by replacing the '/samples/' with the dir_name.
    """
    match = re.search(r'/samples/', path)
    if match:
        modified_path = re.sub(r'/samples/', dir_name, path)
    else:
        print("No match found.")
    return modified_path

def line_profiler_test(func, *args):
    print(f'$$$$$$$$ line profiler processing $$$$$$$$')
    from line_profiler import LineProfiler
    lp = LineProfiler()
    #TODO: add function to lp
    lp_wrapper = lp(func)
    ret = lp_wrapper(*args)
    lp.print_stats()
    print(f'$$$$$$$$')
    return ret

def inverse_sigmoid(x, eps=1e-5):
    """Inverse function of sigmoid.

    Args:
        x (Tensor): The tensor to do the
            inverse.
        eps (float): EPS avoid numerical
            overflow. Defaults 1e-5.
    Returns:
        Tensor: The x has passed the inverse
            function of sigmoid, has same
            shape with input.
    """
    x = x.clamp(min=0, max=1)
    x1 = x.clamp(min=eps)
    x2 = (1 - x).clamp(min=eps)
    return torch.log(x1 / x2)

# get corner coors
def find_corners(voxel_coor, grid_size):
    """
    Convert voxel coordinates to voxel corners.
    Params:
        voxel_coor: [N, 3]
        voxel_size: [3]
    Returns:
        voxel_corner: [8, N, 3]
    """
    radius_div, angle_div, height_div = grid_size
    
    corner_offsets = torch.tensor([
    [-1, -1, -1], # left front bottom 
    [-1,  1, -1], # left back bottom 
    [ 1, -1, -1], # right front bottom 
    [ 1,  1, -1], # right back bottom 
    [-1, -1,  1], # left front upper 
    [-1,  1,  1], # left back upper 
    [ 1, -1,  1], # right front upper 
    [ 1,  1,  1], # right back upper 
    ], dtype=torch.long)
    corner_offsets = (corner_offsets/2.).to(voxel_coor.device)

    corners = voxel_coor.expand(8, -1, -1)+corner_offsets.unsqueeze(1)+0.5

    # Ensuring that the corner indices are within the valid range
    corners = torch.stack([
    corners[..., 0].clamp(0, radius_div - 1),
    corners[..., 1].clamp(0, angle_div - 1),
    corners[..., 2].clamp(0, height_div - 1)
    ], dim=-1)

    return corners

def get_reference_points(spatial_shapes, valid_ratios, device):
    reference_points_list = []
    for lvl, (H_, W_) in enumerate(spatial_shapes):
        ref_y, ref_x = torch.meshgrid(torch.linspace(0.5, H_ - 0.5, H_, dtype=torch.float32, device=device), # center of each grid
                                    torch.linspace(0.5, W_ - 0.5, W_, dtype=torch.float32, device=device))
        ref_y = ref_y.reshape(-1)[None] / (valid_ratios[:, None, lvl, 1] * H_)
        ref_x = ref_x.reshape(-1)[None] / (valid_ratios[:, None, lvl, 0] * W_)
        ref = torch.stack((ref_x, ref_y), -1)
        reference_points_list.append(ref)
    reference_points = torch.cat(reference_points_list, 1)
    reference_points = reference_points[:, :, None] * valid_ratios[:, None]
    return reference_points


def point_to_voxel_coordinates(point_coords, point_cloud_range, voxel_size, return_res_coords=False):
    """
    Convert point coordinates to voxel coordinates.
    Params:
        point_coords: [N, 3]
        point_cloud_range: [min_radius, min_angle, min_height, max_radius, max_angle, max_height]
        voxel_size: [radius_div, angle_div, height_div], default is [480, 360, 32]
    Returns:
        res_coors: [N, 3]
    """
    polar_res_clamp = clamp_point_polar(point_coords, point_cloud_range)
    min_bound = point_cloud_range[:3].clone().detach()
    max_bound = point_cloud_range[3:].clone().detach()

    # Convert to voxel coordinates
    res_coors = torch.floor((polar_res_clamp - min_bound) / torch.tensor(voxel_size)).int()
    voxel_coors, point2voxel_map = scatter_point_to_voxel(res_coors, point_coords.device)

    if return_res_coords:
        return polar_res_clamp, voxel_coors, point2voxel_map, res_coors
    else:
        return polar_res_clamp, voxel_coors, point2voxel_map

def scatter_point_to_voxel(voxel_coor, device):
    from mmdet3d.models.data_preprocessors.voxelize import dynamic_scatter_3d
    
    voxel_coor = voxel_coor.to(device)
    pseudo_tensor = voxel_coor.new_ones([voxel_coor.shape[0], 1]).float()
    voxel_len, s_voxel_coor, point2voxel_map = dynamic_scatter_3d(pseudo_tensor,
                                                voxel_coor, 'sum', True)

    return s_voxel_coor, point2voxel_map

def clamp_point_polar(point_coords, point_cloud_range):
    """
    Clamp point coordinates to the point cloud range.
    Params:
        point_coords: [N, 3 or 4]
        point_cloud_range: [min_radius, min_angle, min_height, max_radius, max_angle, max_height]
    Returns:
        res_coors: [N, 3 or 4]
    """
    # Convert to a tensor if the input is not already a tensor
    if not isinstance(point_coords, torch.Tensor):
        raise ValueError("Input point_coords must be a tensor.")

    # Define min and max bounds
    min_bound = torch.tensor(point_cloud_range[:3]).to(point_coords.device)
    max_bound = torch.tensor(point_cloud_range[3:]).to(point_coords.device)

    # Clamp coordinates to within the point cloud range
    clamped_point_coords = torch.clamp(point_coords[:, :3], min_bound, max_bound)
    clamped_point_coords = torch.cat((clamped_point_coords, point_coords[:, 3:]), dim=1)

    return clamped_point_coords

def normalize_coors(coors, point_cloud_range):
    """
    Normalize voxel index coordinates to the point cloud range.
    Params:
        coors: tensor[N, 3 or 4]
        point_cloud_range: list[6]:
    Returns:
        normed_coors: tensor[N, 3 or 4]
    """
    range = (torch.tensor(point_cloud_range[3:])-torch.tensor(point_cloud_range[:3])).to(coors.device)
    min_bound = torch.tensor(point_cloud_range[:3]).to(coors.device)
    normed_coors = (coors[:, :3]-min_bound)/range
    normed_coors = torch.cat((normed_coors, coors[:, 3:]), dim=1)
    return normed_coors

def normalize_clamp_coors(points_coords, polar_range, cart_range, ret_p=False, ret_c=False):
    """
    Normalize and clamp point coordinates to the point cloud range.
    Params:
        points_coords: tensor[N, 3]
        polar_range: list[6]
        cart_range: list[6]
    Returns:
        normed_polar_coor: tensor[N, 3]
        normed_cart_coor: tensor[N, 3]
    """
    assert ret_p or ret_c, "At least one of ret_p and ret_c should be True."
    
    clamped_point_cart = clamp_point_polar(points_coords, cart_range) # [N, 3]
    normed_cart_coor = normalize_coors(clamped_point_cart, cart_range)
    if ret_c and not ret_p:
        return normed_cart_coor
    
    polar_coor = cartesian2polar_torch(points_coords, aux_feat=False)
    clamped_point_polar = clamp_point_polar(polar_coor, polar_range)
    normed_polar_coor = normalize_coors(clamped_point_polar, polar_range)
    if ret_p and not ret_c:
        return normed_polar_coor
    
    return normed_polar_coor, normed_cart_coor

def range_split(voxel_coors, grid_size, split_num=3):
    """ Split voxel coordinates along the range dimension. """
    assert voxel_coors.shape[1] == 3, "Voxel coordinates should have 3 dimensions."
    dist_thresh = range(0, grid_size[0], grid_size[0]//split_num)
    dist_mask = torch.zeros_like(voxel_coors[:, 0])
    for i in range(split_num):
        if i == 0:
            dist_mask[(voxel_coors[:, 0] < dist_thresh[i+1])] = i+1
        elif i == split_num - 1:
            dist_mask[(voxel_coors[:, 0] >= dist_thresh[i])] = i+1
        else:
            dist_mask[(voxel_coors[:, 0] >= dist_thresh[i]) & (voxel_coors[:, 0] < dist_thresh[i+1])] = i+1
    return dist_mask


def split_voxel_coors(voxel_coors, batch_size):
    ''' Split voxel coors along batch dimension.'''
    assert voxel_coors.shape[1] == 4, "Voxel coordinates should have 4 dimensions."
    voxel_coors_list = []
    for i in range(batch_size):
        v = voxel_coors[voxel_coors[:, 0] == i][:, 1:] # split voxel coors along batch dimension
        voxel_coors_list.append(v) # (N, 3)
    return voxel_coors_list

def compress_mask(mask, scale_factor=1.):
    if not isinstance(mask, np.ndarray):
        mask = mask.cpu().numpy()
    n, h, w = mask.shape
    new_h, new_w = int(h * scale_factor), int(w * scale_factor)
    new_mask = np.zeros((new_h, new_w), dtype=np.int16)

    for i in range(n):
        resized_mask = resize(mask[i], (new_h, new_w), order=0, preserve_range=True, anti_aliasing=False).astype(bool)
        labeled_mask = label(resized_mask)

        current_pixels = labeled_mask > 0
        existing_pixels = new_mask > 0
        overlap_pixels = current_pixels & existing_pixels
        overlap_count = overlap_pixels.sum()
        new_mask[labeled_mask > 0] = i + 1
    return new_mask

def expand_mask_torch(a: torch.Tensor, scale_factor=1/0.4, dim_expand=False,
                      ignore_ids=(0,),
                      dtype=torch.bool):
    device = a.device

    if scale_factor != 1.0:
        if dim_expand:
            H, W = a.shape
        else:
            N, H, W = a.shape
        Hp = max(1, int(round(H * float(scale_factor))))
        Wp = max(1, int(round(W * float(scale_factor))))
        if dim_expand:
            size = (Hp, Wp)
        else:
            size = (N, Hp, Wp)
        # F.interpolate 对整型不友好，转 float 再转回，mode='nearest' 保证数值不变
        a_scaled = F.interpolate(
            a.unsqueeze(0).unsqueeze(0).to(torch.float32),
            size=size, mode='nearest'
        ).squeeze(0).squeeze(0).to(a.dtype)
    else:
        a_scaled = a
        Hp, Wp = H, W
    
    if dim_expand:
        ids = torch.unique(a_scaled)
        if ignore_ids is not None and len(ignore_ids) > 0:
            ignore_ids_t = torch.tensor(ignore_ids, device=device, dtype=ids.dtype)
            keep = ~(ids.view(-1, 1) == ignore_ids_t.view(1, -1)).any(dim=1)
            ids = ids[keep]

        if ids.numel() == 0:
            empty = torch.empty(0, Hp, Wp, device=device, dtype=dtype)
            return empty, ids

        b = (ids.view(-1, 1, 1) == a_scaled.unsqueeze(0))  # bool

        if dtype is not torch.bool:
            b = b.to(dtype)
    else:
        b = a_scaled.to(dtype)

    return b


def expand_mask(mask, target_size):
    """expand mask of shape [H,W] to target size and return binary masks of shape [N, H, W]"""
    mask = resize(mask, target_size, order=0)
    
    unique_ids = np.unique(mask)
    K = len(unique_ids)
    binary_masks = np.zeros((K-1, mask.shape[0], mask.shape[1]), dtype=np.uint8)
    for i,e in enumerate(unique_ids):
        if i == 0:
            continue
        binary_masks[i] = (mask == e).astype(np.uint8)
    return binary_masks

def vote_nms_python(pcds_fg, pcds_center, dist_thresh, vote_thresh):
    """
    A Python version of vote_nms using NumPy.
    
    :param pcds_fg: Points of interest (foreground points), array of shape (N, 3) for 3D points.
    :param pcds_center: Center points corresponding to each foreground point, array of shape (N, 3).
    :param dist_thresh: Distance threshold for NMS.
    :param vote_thresh: Voting threshold for keeping a point.
    :return: Boolean mask indicating points to keep.
    """
    N = pcds_fg.shape[0]
    keep = np.ones(N, dtype=bool)
    
    for i in range(N):
        if not keep[i]:
            continue
        
        for j in range(i + 1, N):
            dist = np.linalg.norm(pcds_center[i] - pcds_center[j])
            if dist < dist_thresh:
                if pcds_fg[i] > pcds_fg[j]:
                    keep[j] = False
                else:
                    keep[i] = False
    
    return keep

def trans_mask(input, origm, newm):
    '''
    transfrom input mask from origm to newm shape.
    '''
    assert input.shape == origm.shape, 'input and origm should have the same shape'
    a = torch.zeros(newm.shape[0]).to(input)
    a[newm>0] = input[origm>0]
    return a

def p2v_aggregate(point2voxel_map, points_coords, aggreate_mode='max'):
    """
    Aggregate points to voxels.
    Params:
        point2voxel_map: [N_p]
        points_coords: [N_p, D]
        aggreate_mode: 'max' or 'avg' or 'set'
    Returns:
        out: [N_v, D]
    """
    assert points_coords.shape[0] == point2voxel_map.shape[0]
    num_voxel = point2voxel_map.max()+1
    feat_dim = points_coords.shape[1]
    
    point2voxel_map = point2voxel_map.unsqueeze(1).expand(-1, feat_dim).to(points_coords.device) # [1, N_p] -> [N_p, D]
    out = points_coords.new_zeros((feat_dim, num_voxel))
    if aggreate_mode == 'max':
        from torch_scatter import scatter_max
        out, _ = scatter_max(points_coords.T, point2voxel_map.T, out=out)
    elif aggreate_mode == 'avg':
        from torch_scatter import scatter_mean
        out = scatter_mean(points_coords.T, point2voxel_map.T, out=out)
    elif aggreate_mode == 'set':
        from torch_scatter import scatter_max, scatter_mean
        out_max, _ = scatter_max(points_coords.T, point2voxel_map.T, out=out)
        out_avg = scatter_mean(points_coords.T, point2voxel_map.T, out=out)
        out = 0.5*(out_max+out_avg)
        
    return out.T # [N_v, D] 

def cartesian2polar_torch(cartesian_coor, aux_feat=True):
    #TODO: duplicate code with tools/projection/pc2img.py
    x = cartesian_coor[:, 0]
    y = cartesian_coor[:, 1]
    z = cartesian_coor[:, 2]
    r = torch.sqrt(x ** 2 + y ** 2)
    theta = torch.atan2(y, x)
    if aux_feat:
        return torch.stack([r, theta, z, cartesian_coor[:, -1]], dim=1)
    else:
        return torch.stack([r, theta, z], dim=1)
    
def remap_instance_mask(inst_mask):
    unique_vals, inverse_indices = torch.unique(inst_mask, return_inverse=True)
    remapped_mask = inverse_indices.view(inst_mask.shape)
    return remapped_mask

def clone_detach_SparseConvTensor(sparse_tensor):
    from mmcv.ops import SparseConvTensor
    """
    Clone and detach a SparseConvTensor.
    """
    features_copy = sparse_tensor.features.clone().detach()
    indices_copy = sparse_tensor.indices.clone().detach()

    sparse_tensor_copy = SparseConvTensor(
    features_copy,
    indices_copy,
    sparse_tensor.spatial_shape,
    sparse_tensor.batch_size
    )

    return sparse_tensor_copy
