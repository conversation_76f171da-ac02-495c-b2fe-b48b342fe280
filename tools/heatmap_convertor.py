import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import numba as nb
import matplotlib.pyplot as plt
import os
import sys
import cv2
import torch
try:
    import pan_lib.cuda_kernel as cuda_kernel
except:
    print('pan_lib is not installed')
import fpsample
try:
    from mmdet.models.utils import multi_apply
except:
    print('mmdet is not installed')

# class-wise center heatmap generator
class PanopticLabelGenerator_MC(object):
    def __init__(self, grid_size, sigma=5, polar=False):
        """Initialize panoptic ground truth generator.

        Args:
            grid_size: Tuple representing the voxel grid size.
            sigma (int, optional): Gaussian distribution parameter for creating heatmap. Defaults to 5.
            polar (bool, optional): Whether the coordinates are in polar form. Defaults to False.
        """
        self.grid_size = grid_size
        self.polar = polar
        self.sigma = sigma
        # Create a Gaussian kernel
        size = 6 * sigma + 3
        x = np.arange(0, size, 1, float)
        y = x[:, np.newaxis]
        x0, y0 = 3 * sigma + 1, 3 * sigma + 1
        self.g = np.exp(- ((x - x0) ** 2 + (y - y0) ** 2) / (2 * sigma ** 2))
    
    def __call__(self, inst, xyz, bev_inst, class_labels, label_dict, min_bound, intervals, 
                 class_is_inst=range(1, 11),
                 use_offset=False, use_height=False):
        """Generate class-specific instance center and offset ground truth.

        Args:
            inst: Instance panoptic label (N).
            xyz: Point location (N x 3).
            voxel_inst: Voxel panoptic label on the BEV (H x W).
            class_labels: Class labels for each instance (N).
            label_dict: Unique instance label dictionary.
            min_bound: Space minimal bound.
            intervals: Voxelization intervals.
            class_is_inst: valid instance classes.

        Returns:
            centers: A dictionary of class-specific center heatmaps. Each class that is an instance has a heatmap.
            center_pts: List of center points for each instance.
        """
        height, width = self.grid_size[0],self.grid_size[1]
        Z = self.grid_size[2]
        assert Z > 1, 'Height map requires Z > 1'

        unique_classes = np.unique(class_labels)
        unique_classes = np.intersect1d(unique_classes, class_is_inst)
        centers = {cls: np.zeros((height, width), dtype=np.float32) for cls in unique_classes}
        center_pts = []
        offset = np.zeros((2, height, width), dtype=np.float32) if use_offset else None
        heightmap = np.zeros((1, height, width), dtype=np.float32) if use_height else None


        if inst.size < 2:
            return centers, center_pts, offset, heightmap
        
        for cls in unique_classes:
            cls_mask = class_labels == cls
            inst_cls = inst[cls_mask]
            xyz_cls = xyz[cls_mask]
            
            inst_labels = np.unique(inst_cls)
            for inst_label in inst_labels:
                mask = np.where(inst_cls == inst_label)
                bev_mask_idx = np.where(bev_inst == inst_label)
                # print('bev_mask_idx', bev_mask_idx[0].shape)
                if bev_mask_idx[0].shape[0] == 0: continue
                # if label_dict:
                #     voxel_mask = np.where(voxel_inst == label_dict.get(inst_label, inst_label))
                # else:
                #     voxel_mask = np.where(voxel_inst == inst_label)
                
                center_x, center_y = np.mean(xyz_cls[mask, 0]), np.mean(xyz_cls[mask, 1])
                # if cls in (2,3,5):
                #     print('cls, center_x, center_y',cls, center_x, center_y)
                if not self.polar:
                    center_x, center_y = np.sqrt(center_x**2 + center_y**2), np.arctan2(center_y, center_x)
                
                x, y = int(np.floor((center_x - min_bound[0]) / intervals[0])), int(np.floor((center_y - min_bound[1]) / intervals[1]))
                center_pts.append([cls, x, y])
                if use_height:
                    center_z = np.mean(xyz_cls[mask, 2])
                    # print('center_z', center_z, 'min_bound[2]', min_bound[2], 'intervals[2]', intervals[2], 'Z', Z)
                    z = int(np.floor((center_z - min_bound[2]) / intervals[2])) / Z
                
                if 0 <= x < height and 0 <= y < width:
                    sigma = self.sigma
                    ul = int(np.round(x - 3 * sigma - 1)), int(np.round(y - 3 * sigma - 1))
                    br = int(np.round(x + 3 * sigma + 2)), int(np.round(y + 3 * sigma + 2))
                    
                    c, d = max(0, -ul[0]), min(br[0], height) - ul[0]
                    a, b = max(0, -ul[1]), min(br[1], width) - ul[1]
                    
                    cc, dd = max(0, ul[0]), min(br[0], height)
                    aa, bb = max(0, ul[1]), min(br[1], width)
                    
                    centers[cls][cc:dd, aa:bb] = np.maximum(centers[cls][cc:dd, aa:bb], self.g[c:d, a:b])
                    
                    if use_offset:
                        offset[:,bev_mask_idx[0],bev_mask_idx[1]] = np.array([x,y])[..., None] - bev_mask_idx
                    # A = np.array([x,y])[..., None] - bev_mask_idx
                    # if A.shape[-1]>0 and A.max() > 10:
                        # print('class:', cls, 'inst_label', inst_label, 'offset', A, 'center', np.array([x,y]), 'bev_mask_idx', bev_mask_idx)
                    if use_height:
                        heightmap[0, cc:dd, aa:bb] = np.maximum(
                            heightmap[0, cc:dd, aa:bb], self.g[c:d, a:b] * z)
                        
        # Ensure that the center heatmap has at least 1.0 at the center point
        for cls, x, y in center_pts:
            if np.unique(centers[cls]).shape[0] > 1:
                continue
            # print('cls, x, y',cls, x, y)
            if x < 0 or y < 0 or x >= height or y >= width:
                continue
            # print('cls, x, y',cls, x, y)
            centers[cls][x, y] = max(centers[cls][x, y], 1.0)  # 确保中心点至少是1
        
        return centers, center_pts, offset, heightmap


# binary center heatmap and offset generator
class PanopticLabelGenerator(object):
    def __init__(self,grid_size,sigma=5,polar=False):
        """Initialize panoptic ground truth generator

        Args:
            grid_size: voxel size.
            sigma (int, optional):  Gaussian distribution paramter. Create heatmap in +-3*sigma window. Defaults to 5.
            polar (bool, optional): Is under polar coordinate. Defaults to False.
        """        
        self.grid_size = grid_size
        self.polar = polar

        self.sigma = sigma
        size = 6 * sigma + 3
        x = np.arange(0, size, 1, float)
        y = x[:, np.newaxis]
        x0, y0 = 3 * sigma + 1, 3 * sigma + 1
        self.g = np.exp(- ((x - x0) ** 2 + (y - y0) ** 2) / (2 * sigma ** 2))
    
    def __call__(self, inst, xyz, voxel_inst, voxel_position, label_dict, min_bound, intervals,
                 use_offset=False, use_height=False):
        """Generate instance center and offset ground truth

        Args:
            inst : instance panoptic label (N)
            xyz : point location (N x 3)
            voxel_inst : voxel panoptic label on the BEV (H x W)
            voxel_position : voxel location on the BEV (3 x H x W)
            label_dict : unqiue instance label dict
            min_bound : space minimal bound
            intervals : voxelization intervals

        Returns:
            center, center_pts, offset
        """        
        height, width = self.grid_size[0],self.grid_size[1]
        Z = self.grid_size[2]
        assert Z > 1, 'Height map requires Z > 1'
        
        center = np.zeros((1, height, width), dtype=np.float32)
        center_pts = []
        offset = np.zeros((2, height, width), dtype=np.float32) if use_offset else None
        heightmap = np.zeros((1, height, width), dtype=np.float32) if use_height else None
        
        #skip empty instances
        if inst.size < 2: return center, center_pts, offset, heightmap
        # find unique instances
        inst_labels = np.unique(inst)
        for inst_label in inst_labels:
            # get mask for each unique instance
            mask = np.where(inst == inst_label)
            if label_dict:
                voxel_mask = np.where(voxel_inst == label_dict[inst_label])
            else:
                voxel_mask = np.where(voxel_inst == inst_label)
            # get center
            center_x, center_y = np.mean(xyz[mask,0]), np.mean(xyz[mask,1]) # 《---- TODO:这里有问题
            # if self.polar:
            #     # convert to polar coordinate
            #     center_x_pol, center_y_pol = np.sqrt(center_x**2 + center_y**2),np.arctan2(center_y,center_x)
            #     center_x = center_x_pol
            #     center_y = center_y_pol

            # generate center heatmap
            x, y = int(np.floor((center_x-min_bound[0])/intervals[0])), int(np.floor((center_y-min_bound[1])/intervals[1]))
            center_pts.append([x, y])
            if use_height: 
                center_z = np.mean(xyz[mask,2])
                z = int(np.floor((center_z-min_bound[2])/intervals[2]))/Z # normalize height
                
            # outside image boundary
            if x < 0 or y < 0 or \
                    x >= height or y >= width:
                x = np.clip(x, 0, height - 1)
                y = np.clip(y, 0, width - 1)
                # continue
            sigma = self.sigma
            # upper left
            ul = int(np.round(x - 3 * sigma - 1)), int(np.round(y - 3 * sigma - 1))
            # bottom right
            br = int(np.round(x + 3 * sigma + 2)), int(np.round(y + 3 * sigma + 2))

            if self.polar:
                c, d = max(0, -ul[0]), min(br[0], height) - ul[0]
                a, b = 0, br[1] - ul[1]

                cc, dd = max(0, ul[0]), min(br[0], height)
                angle_list = [angle_id % width for angle_id in range(ul[1],br[1])]
                center[0, cc:dd, angle_list] = np.maximum(
                    center[0, cc:dd, angle_list], np.transpose(self.g[c:d,a:b]))
                if use_height:
                    heightmap[0, cc:dd, angle_list] = np.maximum(
                        heightmap[0, cc:dd, angle_list], np.transpose(self.g[c:d,a:b])*z)

            else:
                c, d = max(0, -ul[0]), min(br[0], height) - ul[0]
                a, b = max(0, -ul[1]), min(br[1], width) - ul[1]

                cc, dd = max(0, ul[0]), min(br[0], height)
                aa, bb = max(0, ul[1]), min(br[1], width)
                center[0, cc:dd, aa:bb] = np.maximum(
                    center[0, cc:dd, aa:bb], self.g[c:d,a:b])
                if use_height:
                    heightmap[0, cc:dd, aa:bb] = np.maximum(
                        heightmap[0, cc:dd, aa:bb], self.g[c:d,a:b]*z)
            # print(inst_label)
            # print('----', voxel_mask)
            if use_offset:
                if self.polar:
                    # generate offset (2, h, w) -> (y-dir, x-dir)
                    offset_x = ((center_x - voxel_position[0,voxel_mask[0],voxel_mask[1]])/intervals[0]).squeeze(-1)
                    offset_y = (((center_y - voxel_position[1,voxel_mask[0],voxel_mask[1]]+np.pi)%(2*np.pi) - np.pi)/intervals[1]).squeeze(-1)
                    # if offset_x.shape[0]>0 and offset_x.max() > 10:
                    #     print('inst_label', inst_label, 'offset_x', offset_x, 'center_x', center_x, 'voxel_position_x', voxel_position[0,voxel_mask[0],voxel_mask[1]], 'x:', x,'voxel_position_y', voxel_position[1,voxel_mask[0],voxel_mask[1]], 'center_y', center_y, 'y:',y)
                    offset[0,voxel_mask[0],voxel_mask[1]] = offset_x
                    offset[1,voxel_mask[0],voxel_mask[1]] = offset_y
                else:
                    # generate offset (2, h, w) -> (y-dir, x-dir)
                    offset[0,voxel_mask[0],voxel_mask[1]] = (center_x - voxel_position[0,voxel_mask[0],voxel_mask[1]])/intervals[0]
                    offset[1,voxel_mask[0],voxel_mask[1]] = (center_y - voxel_position[1,voxel_mask[0],voxel_mask[1]])/intervals[1]

        return center, center_pts, offset, heightmap


@nb.jit('u1[:,:](u1[:,:],i8[:,:])', cache=True, parallel=False, nopython=True)
def nb_process_inst(processed_inst, sorted_inst_voxel_pair):
    label_size = 256
    counter = np.zeros((label_size,), dtype=np.uint16)
    counter[sorted_inst_voxel_pair[0, 2]] = 1
    cur_sear_ind = sorted_inst_voxel_pair[0, :2]
    for i in range(1, sorted_inst_voxel_pair.shape[0]):
        cur_ind = sorted_inst_voxel_pair[i, :2]
        if not np.all(np.equal(cur_ind, cur_sear_ind)):
            processed_inst[cur_sear_ind[0], cur_sear_ind[1]] = np.argmax(counter)
            counter = np.zeros((label_size,), dtype=np.uint16)
            cur_sear_ind = cur_ind
        counter[sorted_inst_voxel_pair[i, 2]] += 1
    processed_inst[cur_sear_ind[0], cur_sear_ind[1]] = np.argmax(counter)
    return processed_inst

def seq2bev(target_seq, refer_seq, grid_size):
    """
    Convert sequence to BEV
    Args:
        target_seq: target sequence, [N, 1]. E.g. voxel_semantic_mask, voxel_instance_mask.
        refer_seq: reference sequence that indicate mapping relation BEV location of target_seq, [N, 2]. E.g. voxel_coor.
        grid_size: grid size
    """
    grid_size = np.array(grid_size) if not isinstance(grid_size, np.ndarray) else grid_size
    min_bound = np.array([0, 0, 0])
    refer_seq = np.clip(refer_seq, 0, grid_size-1) # clip to grid_size-1, to avoid out of range
    
    bev = np.zeros(grid_size)
    bev[refer_seq[:, 0], refer_seq[:, 1], 0] = target_seq[:]
    return bev

def calculate_voxel_positions(point_cloud_range, grid_size):
    point_cloud_range = np.array(point_cloud_range)
    # 计算每个voxel在每个维度上的大小
    voxel_size = (point_cloud_range[3:] - point_cloud_range[:3]) / grid_size

    # 创建一个三维网格
    grid_x, grid_y, grid_z = np.meshgrid(
        np.arange(grid_size[0]),
        np.arange(grid_size[1]),
        np.arange(grid_size[2]),
        indexing='ij'
    )

    # 计算每个voxel的位置
    voxel_positions = np.stack([
        grid_x * voxel_size[0] + point_cloud_range[0],
        grid_y * voxel_size[1] + point_cloud_range[1],
        grid_z * voxel_size[2] + point_cloud_range[2]
    ], axis=-1)

    return voxel_positions

def calculate_heatmap(input, plgc):
    
    points = input['lidar_point']
    points_instance_labels = input['pts_instance_mask']
    points_semantic_labels = input['pts_semantic_mask']
    voxel_instance_labels = input['voxel_instance_labels']
    voxel_semantic_labels = input['voxel_semantic_labels']
    bev_instance_labels = input['bev_instance_labels']
    bev_voxel_position = input['voxel_positions']
    min_bound = input['min_bound']
    grid_size = input['grid_size']
    point_cloud_range_polar = input['point_cloud_range_polar']
    
    intervals = (np.array(point_cloud_range_polar[3:])-np.array(point_cloud_range_polar[:3])) / np.array(grid_size)

    center, center_pts = plgc(points_instance_labels, points, 
                                 bev_instance_labels, points_semantic_labels, label_dict=None,
                                 min_bound=min_bound, intervals=intervals)
    input['center'] = center
    input['center_pts'] = center_pts
    return input

# def bev2seq(bev_data, voxel_coor):
#     """
#     transform bev heatmap to sequence along the voxel coor
#     params:
#     - hm_bev: torch.Tensor, shape (H, W, C)
#     - voxel_coor: torch.Tensor, shape (N, 2)
#     return:
#     - voxel_data: torch.Tensor, shape (N, C)
#     """
#     H, W, C = bev_data.shape
#     indices = voxel_coor[:, 0] * W + voxel_coor[:, 1]
#     indices = indices.unsqueeze(1).expand(-1, C)  # 扩展索引以匹配通道维度

#     voxel_data = torch.gather(bev_data.view(-1, C), 0, indices)
#     return voxel_data


def bev2seq(bev_data, voxel_coor):
    """
    transform bev heatmap to sequence along the voxel coor
    params:
    - hm_bev: torch.Tensor, shape (H, W, C)
    - voxel_coor: torch.Tensor, shape (N, 2)
    return:
    - voxel_data: torch.Tensor, shape (N, C)
    """
    assert voxel_coor.shape[1] == 3, 'voxel_coor should be in shape (N, 3)'
    H, W, C = bev_data.shape
    indices = voxel_coor[:, 0] * W + voxel_coor[:, 1] #TODO-YINING: only consider the first two dimensions
    indices = indices.unsqueeze(1).expand(-1, C).long()  # 扩展索引以匹配通道维度

    voxel_data = torch.gather(bev_data.view(-1, C), 0, indices)
    return voxel_data

# return voxel_mask of top N activations
def find_activation(bev, voxel_coors, B, N=1):
    assert voxel_coors.shape[1] == 3, 'voxel_coor should be in shape (N, 3)'
    """find class-wise activation in voxel space.
    Args:
        - bev: Tensor, shape (H, W, C), BEV heatmap
        - voxel_coor: Tensor, shape (B*V, 4), voxel coordinates
        - N: int, number of activations to find
    Returns:
        - hm_mask_voxel: Tensor, shape (B, N, 1), mask of top N activations in voxel space
    """
    hm_masks_voxel = []
    for i in range(B):
        bev_split = bev[i]
        hm_voxel = bev2seq(bev_split.permute(1,2,0), voxel_coor)
        selected = hm_voxel.permute(1,0).argsort(dim=-1, descending=True)[..., :N] # [C, N]
        hm_mask_voxel = torch.zeros([voxel_coor.shape[0],1], dtype=torch.bool)
        hm_mask_voxel[selected.flatten()] = True
        hm_masks_voxel.append(hm_mask_voxel)   
    return hm_masks_voxel

# return position of top N activations, support all or class-wise selection
def find_activation_candidates_v1(bev, voxel_coor, N=1, mode='agnostic'):
    """find class-wise activation in voxel space.
    Args:
        - bev: Tensor, shape (H, W, C), BEV heatmap
        - voxel_coor: Tensor, shape (B*V, 4), voxel coordinates
        - N: int, number of activations to find
        - mode: str, 'agnostic' or 'class', select top N activations for all classes or each class
    Returns:
        - packed selected: Tensor, shape (N, 2), 1st column is class index, 2nd column is voxel index
    """
    hm_voxel = bev2seq(bev.permute(1,2,0), voxel_coor.long())
    if mode == 'agnostic':
        V, C = hm_voxel.shape
        top_proposals = hm_voxel.permute(1,0).reshape(-1).argsort(dim=-1, descending=True)[:N]
        top_class, top_index = torch.div(top_proposals, V, rounding_mode='floor'), top_proposals % V
        # selected = torch.stack([top_class, top_index], dim=1)
        selected = [top_class, top_index]
    elif mode == 'class':
        hm_candidates = hm_voxel.permute(1,0).argsort(dim=-1, descending=True)[..., :N] # [1, C, N]
        class_idx = torch.arange(hm_candidates.shape[0]).repeat(hm_candidates.shape[1], 1).t().flatten()
        # selected = torch.stack([class_idx, hm_candidates.flatten()], dim=1)
        selected = [class_idx, hm_candidates.flatten()]
    else:
        raise ValueError(f"The selection mode {mode} is not supported.")
    return selected

def find_activation_candidates_v2(heatmap, num_proposals, mode='agnostic'):
    """
    Find the top N activations in the heatmap.
    Args:
    - heatmap: Tensor, shape (B, C, H, W), BEV heatmap
    - num_proposals: int, number of activations to find
    Returns:
    - top_class: Tensor, shape (B, N), class indices of top N activations
    - top_x: Tensor, shape (B, N), x indices of top N activations, et.al. r in polar coordinates
    - top_y: Tensor, shape (B, N), y indices of top N activations, et.al. theta in polar coordinates
    """
    assert len(heatmap.shape) == 4, 'heatmap should be in shape (B, C, H, W)'
    B, C, H, W = heatmap.shape
    heatmap = heatmap.view(B, C, -1)
    
    if mode == 'agnostic':
        top_proposals = heatmap.view(B, -1).argsort(dim=-1, descending=True)[..., :num_proposals]
        top_class = torch.div(top_proposals, H*W, rounding_mode='floor')
        top_index = top_proposals % (H*W)
        top_x, top_y = torch.div(top_index, W, rounding_mode='floor'), top_index % W
    else:
        raise ValueError(f"The selection mode {mode} is not supported.")
    return top_class, top_index, top_x, top_y

def normalize_heatmap(heatmap):
    "Normalize the heatmap by z-score and min-max normalization."

    heatmap = (heatmap - heatmap.mean()) / heatmap.std()
    heatmap = (heatmap - heatmap.min()) / (heatmap.max() - heatmap.min())
    return heatmap

def normalize_fog(fog):
    """Normalize the fog by sigmoid and min-max normalization."""
    if not isinstance(fog, torch.Tensor):
        fog = torch.tensor(fog, dtype=torch.float32)
    if fog.size(0) == 1 and len(fog.size()) == 3:
        fog = fog.squeeze(0)
        
    fog = torch.sigmoid(fog) 
    fog = (fog - fog.min()) / (fog.max() - fog.min()) 
    return fog
    
# def nms_polar(heatmap, nms_kernel=3):
#     """
#     Non-maximum suppression on polar heatmap.
#     Args:
#     - heatmap: Tensor, shape (C, H, W), polar heatmap
#     - kernel_size: int, kernel size for max pooling
#     Returns:
#     - nms: Tensor, shape (C, H, W), heatmap after nms
#     """
#     assert len(heatmap.shape) == 3, 'heatmap should be in shape (C, H, W)'
    
#     ctr_hmp = heatmap.clone().unsqueeze(0)
#     nms_padding = (nms_kernel - 1) // 2
#     ctr_hmp_max_pooled = F.pad(ctr_hmp,(nms_padding,nms_padding,0,0),mode = 'circular')
#     ctr_hmp_max_pooled = F.max_pool2d(ctr_hmp_max_pooled, kernel_size=nms_kernel, stride=1, padding=(nms_padding,0))
#     if ctr_hmp_max_pooled.shape != ctr_hmp.shape:
#         ctr_hmp_max_pooled = F.interpolate(ctr_hmp_max_pooled, size=(heatmap.shape[1], heatmap.shape[2]), mode='nearest')
#     ctr_hmp[ctr_hmp != ctr_hmp_max_pooled] = 0
#     return ctr_hmp.squeeze(0)

def select_centers(heatmap, voxel_coor, num_cand=128, select_mode='agnostic', nms_kernel=3, thre=0.1, mode='voxel', filter_with_thre=True):
    """
    Select centers from the heatmap.
    Steps:
    - Apply NMS on the heatmap
    - Select top N activations: support class-wise and class-agnostic selection
    - Filter by threshold
    
    Args:
    - heatmap: Tensor, shape (C, H, W), polar heatmap
    - voxel_coor: Tensor, shape (N, 2), voxel coordinates. If mode is 'voxel', this is required.
    - num_cand: int, number of candidates to select
    - select_mode: str, 'agnostic' or 'class', select top N activations for all classes or each class
    - nms_kernel: int, kernel size for max pooling
    - thre: float, threshold for filtering
    - mode: str, 'voxel' or 'bev', select centers in voxel space or BEV space. If 'voxel', some voxel where no valid value will be skipped.
    Returns:
    - selected: Tensor, if 'voxel', shape (N, 2), class index and voxel index. If 'bev', shape (C, H, W), class index and BEV index.
    """
    assert len(heatmap.shape) == 3, 'heatmap should be in shape (C, H, W)'
    
    # # Threshold
    # heatmap[heatmap < thre] = 0
    
    # NMS
    pred_nms = nms_polar(heatmap, nms_kernel)
    
    # select
    if mode == 'voxel':
        assert voxel_coor is not None, 'Current Version only support Method-1, so voxel_coor should not be None'
        selected = find_activation_candidates_v1(pred_nms, voxel_coor, num_cand, select_mode)
        selected_class, selected_index = selected
        top_x, top_y = voxel_coor[selected_index, :][:, 0], voxel_coor[selected_index, :][:, 1]
        # filter by threshold
        if filter_with_thre:
            selected = selected[pred_nms[selected_class, top_x, top_y] > thre]
        return selected[:,0], selected[:,1] # class, voxel index
    
    elif mode == 'bev':
        ctr_all = torch.nonzero(pred_nms > thre)
        if ctr_all.size(0) < num_cand:
            selected = ctr_all
        else:
            # find top k centers.
            top_k_scores, _ = torch.topk(torch.flatten(pred_nms), num_cand)
            selected = torch.nonzero(pred_nms > top_k_scores[-1])
        # return selected # [C, H, W] class, bev_x and bev_y index
        return selected[:,0], selected[:,1:3]# class, bev index (x and y)
    
    
def extract_selected_features(candidates, voxel_feat):
    """
    extract features from top N activations
    Args:
        - candidates: Tensor, shape (N), top N activations
        - voxel_feat: Tensor, shape (V, C), voxel features
    Returns:
        - selected_feat: Tensor, shape (N, C), selected features
    """
    selected_feat = torch.gather(voxel_feat, 0, candidates.unsqueeze(1).expand(-1, voxel_feat.shape[1]))
    return selected_feat
    

def find_class_instance_centers(voxel_semantic_label, voxel_instance_label, voxel_coor, target_classes, max_activations=10):
    """
    根据给定的类别找到实例中心。

    参数:
    - voxel_semantic_label: tensor, 每个 voxel 的语义标签
    - voxel_instance_label: tensor, 每个 voxel 的实例标签
    - voxel_coor: tensor, 每个 voxel 的坐标 (N, 3)
    - target_class: int, 目标类别的标签
    - max_activations: int, 最大激活点数量

    返回:
    - centers: np.array, 实例中心坐标列表
    """
    # 初始化中心坐标列表
    centers = []
    center_indecies = []
    # 筛选出目标类别的 voxels
    for target_class in target_classes:
        target_mask = voxel_semantic_label == target_class
        unique_instance_ids = torch.unique(voxel_instance_label[target_mask])
        original_indices = torch.arange(len(voxel_semantic_label))[target_mask]
        # print(f'unique_instance_ids:{unique_instance_ids} in class:{target_class}')
        for instance_id in unique_instance_ids:
            # 对于每个实例，找到属于它的 voxels
            instance_mask = (voxel_instance_label == instance_id) & target_mask
            
            if instance_mask.sum() == 0:
                continue
            
            # 计算这些 voxels 的坐标中心
            instance_coor = voxel_coor[instance_mask].detach().float()
            center = torch.floor(instance_coor.mean(dim=0)).long()
            centers.append(center)
            
            # instance_indices = original_indices[instance_mask]
            # center_indecies.append(torch.floor(instance_indices.mean(dim=0)))
            center_index = torch.where(instance_mask>0)[0]
            center_index = torch.floor(center_index.float().mean()).long()
            center_indecies.append(center_index)
            # 如果达到了最大激活点数量，停止处理
            if len(centers) >= max_activations:
                print('max_activations', max_activations)
                centers = torch.stack(centers)
                center_indecies = torch.stack(center_indecies)
                return centers, center_indecies
    # # add random points if not enough
    # if len(centers) < max_activations:
    #     random_index = torch.randint(0, voxel_coor.shape[0], (max_activations - len(centers),))
    #     random_points = voxel_coor[random_index]
    #     random_points = torch.tensor(random_points, dtype=torch.long)
    # centers = torch.stack(centers)
    # centers = torch.cat((centers, random_points), dim=0)
    if len(centers)==0:
        centers = [torch.tensor([0,0,0])]
        center_indecies = [torch.tensor(0)]
    centers = torch.stack(centers)
    center_indecies = torch.stack(center_indecies)
    return centers, center_indecies

def extract_set_centoid(points):
    if points.size(0) == 1:
        return torch.zeros(1, dtype=torch.long).to(points), points

    centroid = points.mean(dim=0)
    distances = torch.norm(points - centroid, dim=1)
    closest_index = torch.argmin(distances)

    return closest_index.long(), points[closest_index]

def nms_polar(heatmap, nms_kernel=3):
    """
    Non-maximum suppression on polar heatmap.
    Args:
    - heatmap: Tensor, shape (C, H, W), polar heatmap
    - kernel_size: int, kernel size for max pooling
    Returns:
    - nms: Tensor, shape (C, H, W), heatmap after nms
    """
    # assert len(heatmap.shape) == 3, 'heatmap should be in shape (C, H, W)'
    
    ctr_hmp = heatmap.clone().unsqueeze(0).unsqueeze(0)
    nms_padding = (nms_kernel - 1) // 2
    ctr_hmp_max_pooled = F.pad(ctr_hmp,(nms_padding,nms_padding,0,0),mode = 'circular')
    ctr_hmp_max_pooled = F.max_pool2d(ctr_hmp_max_pooled, kernel_size=nms_kernel, stride=1, padding=(nms_padding,0))
    if ctr_hmp_max_pooled.shape != ctr_hmp.shape:
        ctr_hmp_max_pooled = F.interpolate(ctr_hmp_max_pooled, size=(heatmap.shape[1], heatmap.shape[2]), mode='nearest')
    ctr_hmp[ctr_hmp != ctr_hmp_max_pooled] = 0
    return ctr_hmp.squeeze(0)

def select_centers(heatmap, voxel_coor, num_cand=128, select_mode='agnostic', nms_kernel=3, thre=0.1, mode='voxel', filter_with_thre=True):
    """
    Select centers from the heatmap.
    Steps:
    - Apply NMS on the heatmap
    - Select top N activations: support class-wise and class-agnostic selection
    - Filter by threshold
    
    Args:
    - heatmap: Tensor, shape (C, H, W), polar heatmap
    - voxel_coor: Tensor, shape (N, 2), voxel coordinates. If mode is 'voxel', this is required.
    - num_cand: int, number of candidates to select
    - select_mode: str, 'agnostic' or 'class', select top N activations for all classes or each class
    - nms_kernel: int, kernel size for max pooling
    - thre: float, threshold for filtering
    - mode: str, 'voxel' or 'bev', select centers in voxel space or BEV space. If 'voxel', some voxel where no valid value will be skipped.
    Returns:
    - selected: Tensor, if 'voxel', shape (N, 2), class index and voxel index. If 'bev', shape (C, H, W), class index and BEV index.
    """
    assert len(heatmap.shape) == 3, 'heatmap should be in shape (C, H, W)'
    
    # # Threshold
    # heatmap[heatmap < thre] = 0
    
    # NMS
    pred_nms = nms_polar(heatmap, nms_kernel)
    
    # select
    if mode == 'voxel':
        assert voxel_coor is not None, 'Current Version only support Method-1, so voxel_coor should not be None'
        selected = find_activation_candidates_v1(pred_nms, voxel_coor, num_cand, select_mode)
        selected_class, selected_index = selected # selected[:, 0], selected[:, 1]
        top_x, top_y = voxel_coor[selected_index, :][:, 0], voxel_coor[selected_index, :][:, 1]
        # filter by threshold
        if filter_with_thre:
            selected = selected[pred_nms[selected_class, top_x, top_y] > thre]
        return selected # [C, N] class, voxel index
    
    elif mode == 'bev':
        ctr_all = torch.nonzero(pred_nms > thre)
        if ctr_all.size(0) < num_cand:
            selected = ctr_all
        else:
            # find top k centers.
            top_k_scores, _ = torch.topk(torch.flatten(pred_nms), num_cand)
            selected = torch.nonzero(pred_nms > top_k_scores[-1])
        return selected # [C, H, W] class, bev_x and bev_y index
  
def select_coor_score(heatmap, voxel_coor, index):
    assert voxel_coor.shape[1] == 3, 'voxel_coor should be in shape (N, 3)'
    if voxel_coor.dtype != torch.long:
        voxel_coor = voxel_coor.long()
    if index.dtype != torch.long:
        index = index.long()
    assert len(heatmap.shape) == 3, 'heatmap should be in shape (C, H, W)'
    assert heatmap.shape[0] == 1, 'heatmap should be in shape (1, H, W)'
    cand_vcoor = voxel_coor[index]
    # if cand_vcoor.size(0) == 0:
    #     return cand_vcoor, torch.tensor([]).to(voxel_coor)
    if len(cand_vcoor.shape) == 1:
        cand_vcoor = cand_vcoor.unsqueeze(0)
    cand_score = heatmap[0, cand_vcoor[:, 0], cand_vcoor[:, 1]]
    return cand_vcoor, cand_score

def select_center_bev(heatmap, voxel_coors_input, num_cand=128, nms_kernel=3, thre=0.1, filter_with_thre=True):
    """ When failed to find the voxel coor, search nearby: highest and closed."""
    if len(heatmap.shape) == 3: # compress the class dimension
        heatmap = heatmap.max(0)[0].unsqueeze(0)
    
    if voxel_coors_input.shape[1] == 4:
        voxel_coors = voxel_coors_input[:, :3]
    else:
        voxel_coors = voxel_coors_input
    
    res = []
    
    # NMS
    pred_nms = nms_polar(heatmap, nms_kernel)
    pred_nms = pred_nms.squeeze(0)
    # Thre
    if filter_with_thre:
        ctr_all = torch.nonzero(pred_nms > thre)
    else:
        ctr_all = torch.nonzero(pred_nms)
        
    # Select Top-K
    if ctr_all.size(0) < num_cand:
        # print('ctr_all.size(0) < num_cand:', ctr_all.size(0), num_cand)
        selected = ctr_all
    else:
        # find top k centers.
        # print('ctr_all.size(0) >= num_cand:', ctr_all.size(0), num_cand)
        top_k_scores, _ = torch.topk(torch.flatten(pred_nms), num_cand)
        selected = torch.nonzero(pred_nms > top_k_scores[-1])

    # Map to voxel coor. If failed, search nearby.
    for x, y in zip(selected[:, 0], selected[:, 1]):
        a = torch.where((voxel_coors[:, 0] == x) & (voxel_coors[:, 1] == y))[0]
        
        if a.size(0) == 0:
            # #TODO-YINING: to impl for fast
            # a = search_nearby_highest(x, y, voxel_coors, pred_nms, nms_kernel) 
            # if a is None:
            #     continue
            # if isinstance(a, torch.Tensor):
            #     res.append(a)
            # else:
            #     for aa in a:
            #         res.append(aa)
                            
            b = search_nearby_closed(x, y, voxel_coors, pred_nms, nms_kernel) 
            if b is None:
                continue
            # if x == 106 and y == 126:
            #     print('find nearby closed at:', b)
            if isinstance(b, torch.Tensor):
                res.append(b)
            else:
                for bb in b:
                    res.append(bb)
            # pass
            
        elif a.size(0) > 1: #TODO-YINING: check the case
            # Method-1: select the mean value
            # a = torch.tensor([a.float().mean()]).to(voxel_coors)
            # Method-2: break the a into single element
            for i in range(a.size(0)):
                res.append(torch.tensor([a[i]]).to(voxel_coors))
        else:
            res.append(a)
    if len(res) == 0:
        return None, torch.tensor([]).to(voxel_coors)
    
    # if len(res) > num_cand:
    #     return None, torch.stack(res, dim=0).squeeze().long()[:num_cand]
    return None, torch.stack(res, dim=0).squeeze().long() # class, voxel index: [num_selected]

    
def search_nearby_highest_FAST(x, y, voxel_coors, value_map, k):
    max_x, max_y = value_map.size()
    best_position = None
    max_value = -float('inf')

    # 使用 meshgrid 构建邻域
    grid_x, grid_y = torch.meshgrid(
        torch.arange(-k, k + 1, dtype=torch.long),
        torch.arange(-k, k + 1, dtype=torch.long),
        indexing='ij'
    )
    grid_x, grid_y = grid_x.to(voxel_coors.device), grid_y.to(voxel_coors.device)

    # 限制搜索范围
    neighbors_x = torch.clamp(x + grid_x, 0, max_x - 1)
    neighbors_y = (y + grid_y) % max_y

    # 遍历所有的邻居位置
    for new_x, new_y in zip(neighbors_x.flatten(), neighbors_y.flatten()):
        matched_voxels = torch.where((voxel_coors[:, 0] == new_x) & (voxel_coors[:, 1] == new_y))[0]
        if matched_voxels.size(0) > 0:
            temp_position = voxel_coors[matched_voxels].long()
            value = value_map[temp_position[:, 0], temp_position[:, 1]].max()
            if value > max_value:
                max_value = value
                best_position = matched_voxels

    return best_position

def search_nearby_highest(x, y, voxel_coors, value_map, k):
    max_value = -float('inf')
    best_position = None
    
    # 获取 value_map 的尺寸
    # assert len(value_map.shape) == 2, 'value_map should be in shape (H, W)'
    if len(value_map.shape) == 3 and value_map.shape[0] == 1:
        value_map = value_map.squeeze(0)
    max_x, max_y = value_map.size()
    
    for i in range(-k, k+1):
        for j in range(-k, k+1):
            # 箝位 x 位置在有效范围内
            new_x = torch.clamp(x + i, 0, max_x - 1)
            # 确保 new_y 在 [0, max_y - 1] 范围内
            new_y = (y + j) % max_y if (y + j) >= 0 else (y + j + max_y) % max_y
            # new_x = x + i
            # new_y = y + j
            
            a = torch.where((voxel_coors[:, 0] == new_x) & (voxel_coors[:, 1] == new_y))[0]
            if a.size(0) > 0:
                temp_position = voxel_coors[a].long()
                value = value_map[temp_position[:, 0], temp_position[:, 1]].max()
                if value > max_value:
                    max_value = value
                    best_position = a
    if best_position is not None and best_position.size(0) > 1:
        # best_position = torch.tensor([best_position.float().mean()]).to(voxel_coors)
        # for i in range(best_position.size(0)):
        best_position = [torch.tensor([best_position[i]]).to(voxel_coors) for i in range(best_position.size(0))]
                
    return best_position

def search_nearby_closed(x, y, voxel_coors, value_map, k):
    res = []
    max_value = -float('inf')
    
    max_x, max_y = value_map.size()
    
    for i in range(1, k+1):
        for j in range(1, k+1):
            
            new_x_g = torch.clamp(x + i, 0, max_x - 1)
            new_x_l = torch.clamp(x - i, 0, max_x - 1)
            new_y_g = (y + j) % max_y if (y + j) >= 0 else (y + j + max_y) % max_y
            new_y_l = (y - j) % max_y if (y - j) >= 0 else (y - j + max_y) % max_y
            
            lt = torch.where((voxel_coors[:, 0] == new_x_l) & (voxel_coors[:, 1] == new_y_l))[0]
            lm = torch.where((voxel_coors[:, 0] == new_x_l) & (voxel_coors[:, 1] == y))[0]
            lb = torch.where((voxel_coors[:, 0] == new_x_l) & (voxel_coors[:, 1] == new_y_g))[0]
            mt = torch.where((voxel_coors[:, 0] == x) & (voxel_coors[:, 1] == new_y_l))[0]
            rt = torch.where((voxel_coors[:, 0] == new_x_g) & (voxel_coors[:, 1] == new_y_l))[0]
            rm = torch.where((voxel_coors[:, 0] == new_x_g) & (voxel_coors[:, 1] == y))[0]
            rb = torch.where((voxel_coors[:, 0] == new_x_g) & (voxel_coors[:, 1] == new_y_g))[0]
            mb = torch.where((voxel_coors[:, 0] == x) & (voxel_coors[:, 1] == new_y_g))[0]
            
            # if x in [101, 102, 103, 104, 106, 112, 113, 114, 115] and y in [122, 123, 124, 126, 127, 128, 129]:
                # print('lt, lb, rt, rb:', lt, lb, rt, rb)
                # print('coord:', new_x_l, new_x_g, new_y_l, new_y_g)
            for a in [lt, lm, lb, mt, rt, rm, rb, mb]:
                if a.size(0) == 1:
                   res.append(a) 
                elif a.size(0) > 1:
                    for i in range(a.size(0)):
                        res.append(torch.tensor([a[i]]).to(voxel_coors))
            if len(res) > 0:
                # if x in [101, 102, 103, 104, 106, 112, 113, 114, 115] and y in [122, 123, 124, 126, 127, 128, 129]:
                #     print('res:', x, y, res)
                return res
        return res
    
def select_center_bev_debug(heatmap, voxel_coors_input, num_cand=128, nms_kernel=3, thre=0.1, filter_with_thre=True, ret_center=False):
    """ When failed to find the voxel coor, search nearby: highest and closed."""
    
    if len(heatmap.shape) == 3: # compress the class dimension
        heatmap = heatmap.max(0)[0].unsqueeze(0)
    if len(heatmap.shape) == 2:
        heatmap = heatmap.unsqueeze(0)
    
    if voxel_coors_input.shape[1] == 4:
        voxel_coors = voxel_coors_input[:, :3]
    else:
        voxel_coors = voxel_coors_input
    
    res = []
    
    # normalize the heatmap and filter
    heatmap = normalize_heatmap(heatmap)
    heatmap[heatmap < heatmap.mean()] = 0
    # NMS
    pred_nms = nms_polar(heatmap, nms_kernel).squeeze(0)
    # plot_polar_heatmap(pred_nms)
    # Thre
    ctr_all = torch.nonzero(pred_nms > thre) if filter_with_thre else torch.nonzero(pred_nms)

    # Select Top-K
    if ctr_all.size(0) >= num_cand:
        top_k_scores, _ = torch.topk(pred_nms.flatten(), num_cand)
        selected = torch.nonzero(pred_nms >= top_k_scores[-1])
    else:
        selected = ctr_all

    # Map to voxel coor. If failed, search nearby.
    for x, y in zip(selected[:, 0], selected[:, 1]):
        a = torch.where((voxel_coors[:, 0] == x) & (voxel_coors[:, 1] == y))[0]
        res_a = []
        if a.size(0) == 0:
            #TODO-YINING: not impl work yet, to merge the two functions
            # aa = search_nearby(x, y, voxel_coors, pred_nms, nms_kernel, search_for='both') 
            a = search_nearby_highest_FAST(x, y, voxel_coors, pred_nms, nms_kernel) 
            if a is not None:
                res_a.extend(a) if isinstance(a, list) else res_a.append(a)
                            
            b = search_nearby_closed(x, y, voxel_coors, pred_nms, nms_kernel)
            if b is not None:
                res_a.extend(b) if isinstance(b, list) else res_a.append(b)
            
        elif a.size(0) > 1: #TODO-YINING: check the case
            # Method-1: select the mean value
            # a = torch.tensor([a.float().mean()]).to(voxel_coors)
            # Method-2: break the a into single element
            res_a.append(a)

        else:
            res_a.append(a)
        
        if len(res_a) > 0:
            if not ret_center:
                res += res_a
            else:
                set_ind = torch.cat(res_a, dim=0).to(voxel_coors).long()
            if set_ind.size(0) == 0:
                continue
            set_coor = voxel_coors[set_ind].float()
            cent_ind, cent_coor = extract_set_centoid(set_coor)
            cent_ind = cent_ind.long()
    
            res.append(set_ind[cent_ind])
            
    if len(res) == 0:
        return None, torch.tensor([]).to(voxel_coors)
    
    res_ = []
    for r in res:
        if isinstance(r, torch.Tensor) and r.numel() == 1:
            res_.append(torch.tensor([r]).to(voxel_coors))
        else:
            res_.extend(torch.tensor([t]).to(voxel_coors) for t in r) # when include all the voxels, r will be a multi-element tensor
    return None, torch.stack(res_, dim=0).squeeze().long().to(voxel_coors.device) # class, voxel index: [num_selected]

def gaussian_2d(shape, sigma=1):
    """Generate gaussian map.

    Args:
        shape (list[int]): Shape of the map.
        sigma (float): Sigma to generate gaussian map.
            Defaults to 1.

    Returns:
        np.ndarray: Generated gaussian map.
    """
    m, n = [(ss - 1.) / 2. for ss in shape]
    y, x = np.ogrid[-m:m + 1, -n:n + 1]

    h = np.exp(-(x * x + y * y) / (2 * sigma * sigma))
    h[h < np.finfo(h.dtype).eps * h.max()] = 0
    return h

def create_heatmap_with_opencv(points, kernel_size, heatmap_shape):
    """
    使用OpenCV的腐蚀操作创建一个heatmap，并将目标点周围kernel大小的区域的值设置为1，其余为0

    参数:
    points: Tensor, 目标点位置，形状为 [K, 2]，K为目标点数量，2表示每个点的坐标 (x, y)
    kernel_size: int, kernel的大小 (假设是正方形)
    heatmap_shape: Tuple, heatmap的形状 (H, W)，H为高度，W为宽度

    返回:
    heatmap: Tensor, 生成的heatmap，形状为 (H, W)
    """
    # 创建一个全零的numpy数组作为heatmap
    heatmap = np.zeros(heatmap_shape, dtype=np.uint8)
    
    # 遍历目标点，将点位置的值设置为1
    for point in points:
        x, y = point
        heatmap[x, y] = 1

    # 定义kernel
    # kernel = np.ones((kernel_size, kernel_size), np.uint8) # all 1
    kernel = gaussian_2d([kernel_size, kernel_size], sigma=1) # gaussian kernel

    # 使用OpenCV的膨胀操作，将目标点周围的区域填充为1
    heatmap = cv2.dilate(heatmap, kernel, iterations=1)

    # 将heatmap转为PyTorch张量
    # heatmap = torch.from_numpy(heatmap).float()

    return heatmap

def select_center_voxel_fast(heatmap, voxel_coors, nms_func, num_cand=128, k=5, dim='bev', filter_with_thre=True):
    """ Find the center of the heatmap in BEV space, search nearby for the missing voxel coor."""
    
    if len(heatmap.shape) == 3: # compress the class dimension
        heatmap = heatmap.max(0)[0]
    if len(heatmap.shape) == 2:
        heatmap = heatmap
    
    assert voxel_coors.shape[1] == 3, 'voxel_coors should be in shape (N, 3)'
    
    # Normalize the heatmap and filter
    heatmap = normalize_heatmap(heatmap)
    thre = heatmap.mean()
    heatmap[heatmap < heatmap.mean()] = 0
    # NMS
    pred_nms = nms_polar(heatmap, k).squeeze(0)
    # Thre
    ctr_all = torch.nonzero(pred_nms > thre) if filter_with_thre else torch.nonzero(pred_nms)

    # Select Top-K
    if ctr_all.size(0) >= num_cand*2:
        top_k_scores, _ = torch.topk(pred_nms.flatten(), num_cand*2)
        selected = torch.nonzero(pred_nms >= top_k_scores[-1])
    else:
        selected = ctr_all
    
    # Find regional and nms
    dil_heatmap = create_heatmap_with_opencv(selected.cpu().numpy(), k, (480, 360)) # for-loop:0.002s # cv2: 0.0002s
    dil_heatmap = torch.tensor(dil_heatmap).to(voxel_coors.device).float() if not isinstance(dil_heatmap, torch.Tensor) else dil_heatmap
    voxel_scores = bev2seq((heatmap*dil_heatmap).unsqueeze(-1), voxel_coors).squeeze(-1)
    # print(f'voxel_coors:{voxel_coors}')
    selected_coor, _, selected_ind = nms_func(voxel_coors, voxel_scores, dim=dim)
    selected_coor = selected_coor.long()
    return None, selected_ind

def select_3DQ(heatmap, voxel_coors_list, nms_para):
    # #TODO-YINING: class result is not checked yet 
    point_nms = PointNMS_select(nms_para)
    
    norm_heatmap = normalize_heatmap(heatmap)
    vote_thre  = norm_heatmap.mean() if nms_para['adapt_thresh'] else nms_para['vote_thresh']
    score_thre = norm_heatmap.mean() if nms_para['adapt_thresh'] else nms_para['score_thresh']
    
    # line processing time test
    # from line_profiler import LineProfiler
    # lp = LineProfiler()
    # lp_wrapper = lp(select_center_voxel_fast)
    # hm_candidates_class, ind_3d = lp_wrapper(norm_heatmap, voxel_coors_list, point_nms)
    # print(f'$$$$$$$$')
    # lp.print_stats()
    # print(f'$$$$$$$$')
    # method-2
    # hm_candidates_class, ind_3d = select_center_bev_debug(
    #                             norm_heatmap, voxel_coors_list, 
    #                             num_cand=num_cand, nms_kernel=sel_para['kernel_size'], 
    #                             thre=norm_heatmap.mean(), 
    #                             filter_with_thre=sel_para['hm_thre'], 
    #                             ret_center=sel_para['use_voxel_center'])  #NOTE: selecte centroid or all the voxel coor
    # method-3
    hm_candidates_class, ind_3d = select_center_voxel_fast(norm_heatmap, voxel_coors_list, point_nms, 
                                               k=5, filter_with_thre=True)
    
    cand_vcoor_3d, cand_vscore_3d = select_coor_score(norm_heatmap, voxel_coors_list, ind_3d)
    
    # alreay did the nms in select_center_voxel_fast
    # cand_vcoor_3d, _, selected_ind = point_nms(cand_vcoor_3d, cand_vscore_3d, dim=nms_para['dim'])
    # cand_vscore_3d = cand_vscore_3d[selected_ind]
    # ind_3d = ind_3d[selected_ind]   
    ind_3d = ind_3d.to(voxel_coors_list.device)
    return cand_vcoor_3d.long(), cand_vscore_3d, ind_3d

def select_2DQ(voxel_coor, sam_vmask_2d, sam_vscore_2d, pred_fog, augment_vmask=None, grid_size=[480, 360, 32]):
    """
    Select 2D queries based on 2D candidate mask and score. Use fog and distance as additional factors.
    Parameters:
        data_sample (dict): data sample.
        voxel_coor (tensor): voxel coordinates.
    Returns:
        cand_vcoor_2d (tensor): selected 2D query coordinates.
        cand_vscore_2d (tensor): selected 2D query scores.
    """
    assert voxel_coor.shape[1]==3, 'voxel_coor should have shape [N, 3]'
    assert sam_vmask_2d.shape[0]==voxel_coor.shape[0], 'sam_vmask_2d and voxel_coor should have the same length'
    assert sam_vscore_2d.shape[0]==voxel_coor.shape[0], 'sam_vscore_2d and voxel_coor should have the same length'
    ind_2d = torch.arange(0, sam_vmask_2d.shape[0], device=sam_vmask_2d.device)
    # *fog 
    ## V1: pred_fog->sigmoid->threshold->apply to mask and score
    # fog_mask_bev = data_sample.pred_fog
    # fog_mask_bev = torch.sigmoid(fog_mask_bev.squeeze(0))>fog_thre
    # fog_mask_seq = bev2seq(fog_mask_bev.squeeze(0).unsqueeze(-1), voxel_coor).squeeze(-1)
    # assert fog_mask_seq.shape[0]==sam_vmask_2d.shape[0], 'fog_mask_seq and sam_vmask_2d should have the same length'
    # sam_vmask_2d = sam_vmask_2d*fog_mask_seq
    # sam_vscore_2d = sam_vscore_2d*sam_vmask_2d
    ## V2: pred_fog->sigmoid->min-max norm->apply to score only
    fog_mask_bev = normalize_fog(pred_fog)
    # np.save('output/3/fog_orig.npy', data_sample.pred_fog.detach().cpu().numpy())
    # np.save('output/3/fog_norm.npy', fog_mask_bev.detach().cpu().numpy())
    fog_mask_seq = bev2seq(fog_mask_bev.squeeze(0).unsqueeze(-1), voxel_coor).squeeze(-1)
    # np.save('output/3/fog_mask_seq.npy', fog_mask_seq.detach().cpu().numpy())
    assert fog_mask_seq.shape[0]==sam_vmask_2d.shape[0], 'fog_mask_seq and sam_vmask_2d should have the same length'
    # sam_vmask_2d = sam_vmask_2d*fog_mask_seq
    sam_vscore_2d = sam_vscore_2d*fog_mask_seq
    # print('2', sam_vscore_2d.min(), sam_vscore_2d.max())
    # *dist
    dist_s = voxel_coor[:, 0]/grid_size[0]
    # np.save('output/3/dist.npy', dist_s.detach().cpu().numpy())
    assert dist_s.min()>=0 and dist_s.max()<=1, 'dist_s should be in [0,1]'
    sam_vscore_2d = sam_vscore_2d * dist_s
    # print('3', dist_s.min(), dist_s.max())
    # print('3', sam_vscore_2d.min(), sam_vscore_2d.max())
    # *aug_mask
    if augment_vmask is not None: 
        print('$$$$$$$$$ use augment_vmask $$$$$$$$$$')
        augment_vmask = augment_vmask.to(sam_vmask_2d.device)
        assert augment_vmask.shape[0]==sam_vmask_2d.shape[0], 'augment_mask and sam_vmask_2d should have the same length'
        sam_vmask_2d = sam_vmask_2d*augment_vmask
        sam_vscore_2d = sam_vscore_2d*augment_vmask

        
    # select 2DQ
    cand_vmask_ctr_2d = extract_pointset_centers_torch(sam_vmask_2d, voxel_coor.float())
    # cand_vscore_ctr_2d = sam_vscore_2d * (cand_vmask_ctr_2d>0)
    cand_vcoor_2d = voxel_coor[cand_vmask_ctr_2d>0,:] # [N, 3]
    cand_vscore_2d = sam_vscore_2d[cand_vmask_ctr_2d>0]
    # print('4', cand_vscore_2d.min(), cand_vscore_2d.max())
    ind_2d = ind_2d[cand_vmask_ctr_2d>0]
    # np.save('output/3/voxel_coor.npy', voxel_coor.detach().cpu().numpy())
    # np.save('output/3/cand_vmask_ctr_2d.npy', cand_vmask_ctr_2d.detach().cpu().numpy())
    return cand_vcoor_2d, cand_vscore_2d, ind_2d
 
def select_2DQ_uniScore(voxel_coor, sam_vmask_2d, sam_vscore_2d, ht_pred, augment_vmask=None, grid_size=[480, 360, 32]):
    """
    Use only the heatmap score and distance as the selection criteria.
    """
    assert voxel_coor.shape[1]==3, 'voxel_coor should have shape [N, 3]'
    assert sam_vmask_2d.shape[0]==voxel_coor.shape[0], 'sam_vmask_2d and voxel_coor should have the same length'
    
    ind_2d = torch.arange(0, sam_vmask_2d.shape[0], device=sam_vmask_2d.device)
    
    # heatmap score
    ht_pred = normalize_heatmap(ht_pred)
    ht_seq = bev2seq(ht_pred.squeeze(0).unsqueeze(-1), voxel_coor).squeeze(-1)
    assert ht_seq.shape[0]==sam_vmask_2d.shape[0], 'fog_mask_seq and sam_vmask_2d should have the same length'
    score_2d = ht_seq

    # *dist
    dist_s = voxel_coor[:, 0]/grid_size[0]
    assert dist_s.min()>=0 and dist_s.max()<=1, 'dist_s should be in [0,1]'
    score_2d = score_2d * dist_s
    
    # select 2DQ centers
    cand_vmask_ctr_2d = extract_pointset_centers_torch(sam_vmask_2d, voxel_coor.float())
    cand_vcoor_2d = voxel_coor[cand_vmask_ctr_2d>0,:] # [N, 3]
    cand_vscore_2d = score_2d[cand_vmask_ctr_2d>0]
    ind_2d = ind_2d[cand_vmask_ctr_2d>0]

    return cand_vcoor_2d, cand_vscore_2d, ind_2d
 
def select_3DQ_uniScore(heatmap, voxel_coors_list, nms_para):
    """
    Use only the heatmap score and distance as the selection criteria.
    """
    point_nms = PointNMS_select(nms_para)
    
    norm_heatmap = normalize_heatmap(heatmap)    
    hm_candidates_class, ind_3d = select_center_voxel_fast(norm_heatmap, voxel_coors_list, point_nms, 
                                               k=5, filter_with_thre=True)
    cand_vcoor_3d, hm_score = select_coor_score(norm_heatmap, voxel_coors_list, ind_3d)
    
    dist_s = cand_vcoor_3d[:, 0]/grid_size[0]
    assert dist_s.min()>=0 and dist_s.max()<=1, 'dist_s should be in [0,1]'
    score_3d = hm_score * (1-dist_s)
    return cand_vcoor_3d.long(), score_3d, ind_3d.to(voxel_coors_list.device)


def plot_polar_heatmap(bev_instance_labels):
    # Squeeze the input array to 2D if necessary
    if len(bev_instance_labels.shape) > 2 and bev_instance_labels.shape[2] == 1:
        polar_data = bev_instance_labels.squeeze()
    elif len(bev_instance_labels.shape) == 2:
        polar_data = bev_instance_labels
    else:
        raise ValueError('Invalid input shape for plotting polar heatmap')

    # Create polar axis
    plt.rcParams['figure.figsize'] = [20, 20]
    fig = plt.figure()
    ax = fig.add_subplot(111, polar=True)
    r = np.linspace(0, 1, polar_data.shape[0])
    theta = np.linspace(-np.pi, np.pi, polar_data.shape[1])
    
    c = ax.pcolormesh(theta, r, polar_data, cmap='hot')

    # Add colorbar
    fig.colorbar(c, ax=ax)
    # Off the axis
    ax.set_yticklabels([])
    ax.set_xticklabels([])
    ax.set_yticks([])
    ax.set_xticks([])
    
    plt.show()
    
# def cand_recall_calc(gt_inst, pred_cand, thre=0.5):
#     """
#     Calculate recall for instance segmentation.
#     Args:
#     - gt_inst: Tensor, shape (N), ground truth instance labels on point-wise or voxel-wise.
#     - pred_cand: Tensor, shape (N), selected instance candidates, only part of the instance mask.
#     Returns:
#     - recall: float, recall
#     """
    
#     # re-index the gt_inst
#     # assert is tensor
#     assert isinstance(gt_inst, torch.Tensor), 'input should be a tensor'
#     assert isinstance(pred_cand, torch.Tensor), 'input should be a tensor'
    
#     gt_inst_ = gt_inst.clone().detach() 
#     for idx, p in enumerate(torch.unique(gt_inst)): 
#         gt_inst_[gt_inst_==p] = idx

#     # gt one-hot encoding
#     if isinstance(pred_cand, torch.Tensor):
#         M = torch.unique(gt_inst).shape[0]
#     else:
#         M = np.unique(gt_inst).shape[0] # number of instances
#     gt_inst_onehot = torch.zeros((gt_inst_.shape[0], M)).to(pred_cand.device)
#     gt_inst_onehot.scatter_(1, gt_inst_.unsqueeze(1), 1)
    
#     res = (torch.einsum('pm,p->pm', gt_inst_onehot, pred_cand)).max(dim=0)[0]>0
#     return res
    
# optimize the function
def cand_recall_calc(gt_inst, pred_cand, thre=0):
    """
    Calculate the recall of instance segmentation.
    
    Args:
    - gt_inst: Tensor, shape (N), the ground truth instance label for each point/voxel.
    - pred_cand: Tensor, shape (N), the predicted candidate instance, only contains the mask of some instances.
    - thre: float, IoU threshold, the threshold to judge whether the candidate instance is correctly predicted.
    
    Returns:
    - recall: float, the recall of candidate instance.
    """
    # assert isinstance(gt_inst, torch.Tensor), 'gt_inst should be a tensor'
    # assert isinstance(pred_cand, torch.Tensor), 'pred_cand should be a tensor'
    if not isinstance(gt_inst, torch.Tensor):
        gt_inst = torch.tensor(gt_inst)
    if not isinstance(pred_cand, torch.Tensor):
        pred_cand = torch.tensor(pred_cand)
    
    # Re-index gt_inst to ensure that the instances are numbered starting from 0
    unique_gt, gt_inst_reindexed = torch.unique(gt_inst, return_inverse=True)
    M = unique_gt.shape[0]  # Number of real instances

    # One-hot encoding for gt_inst
    gt_inst_onehot = torch.zeros((gt_inst.shape[0], M), device=gt_inst.device)
    gt_inst_onehot.scatter_(1, gt_inst_reindexed.unsqueeze(1), 1)

    # Calculate the overlap 
    overlaps = torch.einsum('pm,p->pm', gt_inst_onehot, pred_cand.float())
    res = overlaps.max(dim=0)[0] > thre

    return res


def class_wise_recall_cnt(gt_inst, pred_cand, gt_sem, thre=0.5):
    """
    Calculate the class-wise recall to avoid loop for each class. Return the number of true positives and total instances for each class.
    
    Args:
    - gt_inst: Tensor, shape (N), the ground truth instance label for each point/voxel.
    - pred_cand: Tensor, shape (N), the predicted candidate instance, only contains the mask of some instances.
    - gt_sem: Tensor, shape (N), the ground truth semantic label for each point/voxel.
    - thre: float, IoU threshold, the threshold to judge whether the candidate instance is correctly predicted.
    
    Returns:
    - class_recall_dict: dict, the recall of each class.
    """
    
    # Ensure the input is a tensor
    # assert isinstance(gt_inst, torch.Tensor), 'gt_inst should be a tensor'
    # assert isinstance(pred_cand, torch.Tensor), 'pred_cand should be a tensor'
    # assert isinstance(gt_sem, torch.Tensor), 'gt_sem should be a tensor'
    if not isinstance(gt_inst, torch.Tensor):
        gt_inst = torch.tensor(gt_inst)
    if not isinstance(pred_cand, torch.Tensor):
        pred_cand = torch.tensor(pred_cand)
    if not isinstance(gt_sem, torch.Tensor):
        gt_sem = torch.tensor(gt_sem)
    
    # Get the unique values of all semantic classes
    unique_classes = torch.unique(gt_sem)
    num_classes = unique_classes.shape[0]

    # Re-index the instance label
    unique_gt, gt_inst_reindexed = torch.unique(gt_inst, return_inverse=True)
    M = unique_gt.shape[0]  # Number of real instances

    # Create one-hot encoding matrix: size [N, M]
    gt_inst_onehot = torch.zeros((gt_inst.shape[0], M), device=gt_inst.device)
    gt_inst_onehot.scatter_(1, gt_inst_reindexed.unsqueeze(1), 1)

    # Create the mask matrix for each class: size [num_classes, N]
    class_mask = (gt_sem.unsqueeze(0) == unique_classes.unsqueeze(1)).float()

    # Calculate the overlap for each instance: size [N, M]
    overlaps = torch.einsum('pm,p->pm', gt_inst_onehot, pred_cand.float())

    # Calculate the recall for each class
    # Calculate the maximum overlap for each class and compare with the threshold
    max_overlap_per_class = torch.einsum('cp,pm->cm', class_mask, overlaps) > thre
    true_positives_per_class = max_overlap_per_class.float().sum(dim=1)
    total_instances_per_class = (torch.einsum('cp,pm->cm', class_mask, gt_inst_onehot)>0).sum(dim=1)

    # Calculate the recall for each class
    recall_per_class = torch.stack([true_positives_per_class, total_instances_per_class], dim=1)
    class_recall_dict = {int(unique_classes[i].item()): recall_per_class[i] for i in range(num_classes)}    
    return class_recall_dict
    
def comprehensive_recall_calc(batch_gt_vinst, batch_pred_mask, batch_gt_vsem, batch_range_mask, split_num=3, num_thing_cls=10):
    """
    Calculate the comprehensive recall for instance segmentation.
    #NOTE: the gt should be the thing class only.
    """
    tp, total = 0, 0
    recall_cls_cnt = {i: torch.tensor([0., 0.]) for i in range(num_thing_cls+1)}
    recall_range_cnt = {i+1: torch.tensor([0., 0.]) for i in range(split_num)}
    
    for gt_vinst_thing, pred_mask, gt_vsem_thing, range_mask in zip(batch_gt_vinst, batch_pred_mask, batch_gt_vsem, batch_range_mask):
        recall = cand_recall_calc(gt_vinst_thing, pred_mask)
        tp += recall.sum().float()
        total += recall.shape[0]
        
        c = class_wise_recall_cnt(gt_vinst_thing, pred_mask, gt_vsem_thing)
        recall_cls_cnt = update_dict(recall_cls_cnt, c)
        
        d = class_wise_recall_cnt(gt_vinst_thing, pred_mask, range_mask, thre=0.5)
        recall_range_cnt = update_dict(recall_range_cnt, d)
        
    return tp, total, recall_cls_cnt, recall_range_cnt

def update_dict(source, target):
    """ Update the target dict with the source dict, the source must have all the keys"""
    for k, v in source.items():
        if k in target:
            source[k] += target[k]
    return source

def extract_pointset_centers_torch(pts_mask, pts_coor):
    """
    Extract the center point of each point set.
        
    :param pts_mask: torch.Tensor, shape is [N, 1], representing the point set ID of each point.
    :param pts_coor: torch.Tensor, shape is [N, 3], representing the spatial coordinates (x, y, z) of each point.
    :return: pts_center, torch.Tensor, shape is [N, 1], center mask of each point set.
    """
    unique_sets = torch.unique(pts_mask)
    pts_center = torch.zeros_like(pts_coor)
    
    for pointset_id in unique_sets:
        mask = pts_mask == pointset_id
        pointset_coor = pts_coor[mask.squeeze()]
        
        centroid = pointset_coor.mean(dim=0)
        distances = torch.norm(pointset_coor - centroid, dim=1)
        nearest_point_index = torch.argmin(distances)
        pts_center[mask.nonzero(as_tuple=True)[0][nearest_point_index]] = 1
        pts_center = pts_center.max(1)[0].unsqueeze(1)
    return pts_center.squeeze(-1)
    
def vote_nms_python(pcds_fg, pcds_center, dist_thresh, vote_thresh):
    """
    A Python version of vote_nms using NumPy.
    
    :param pcds_fg: Points of interest (foreground points), array of shape (N, 3) for 3D points.
    :param pcds_center: Center points corresponding to each foreground point, array of shape (N, 3).
    :param dist_thresh: Distance threshold for NMS.
    :param vote_thresh: Voting threshold for keeping a point.
    :return: Boolean mask indicating points to keep.
    """
    N = pcds_fg.shape[0]
    keep = np.ones(N, dtype=bool)
    
    for i in range(N):
        if not keep[i]:
            continue
        
        for j in range(i + 1, N):
            dist = np.linalg.norm(pcds_center[i] - pcds_center[j])
            if dist < dist_thresh:
                if pcds_fg[i] > pcds_fg[j]:
                    keep[j] = False
                else:
                    keep[i] = False
    
    return keep


class PointNMS(nn.Module):
    def __init__(self, point_nms_dic):
        super(PointNMS, self).__init__()
        self.point_nms_dic = point_nms_dic
        self.K = self.point_nms_dic['K']
        self.dist_thresh = float(self.point_nms_dic['dist_thresh'])
        self.vote_thresh = float(self.point_nms_dic['vote_thresh'])
        self.score_thresh = float(self.point_nms_dic['score_thresh'])
    
    def forward(self, points, scores):
        '''
        Inputs:
            points: torch.tensor, [N, 3]
            scores: torch.tensor, [N]
        Outputs:
            obj_center: [K, 3]
            keep: [K]
        '''
        assert points.shape[0] == scores.shape[0], 'points and scores should have the same length'
        assert isinstance(points, torch.Tensor), 'points should be a tensor'
        assert isinstance(scores, torch.Tensor), 'scores should be a tensor'
        
        pts_score = torch.cat((points, scores.unsqueeze(-1)), dim=-1)
        N = pts_score.shape[0]
        DEVICE = points.device
        _, indices = pts_score[:, -1].sort(dim=0, descending=True)
        pts_score_order = pts_score[indices]
        pts_score_order = pts_score_order[pts_score_order[:, -1] > self.score_thresh]
        if pts_score_order.size(0) == 0:
            return torch.zeros((0, 3), dtype=torch.float).to(DEVICE), torch.zeros((0,), dtype=torch.long).to(DEVICE), None
        
        col_blocks = (N + 64 - 1) // 64
        obj_center = torch.zeros((self.K, 3), dtype=torch.float).to(DEVICE)
        matching_mat = torch.zeros((N, col_blocks), dtype=torch.long).to(DEVICE)
        matching_mat_vote = torch.zeros((N, col_blocks), dtype=torch.long).to(DEVICE)
        remv = torch.zeros((col_blocks,), dtype=torch.long).to(DEVICE)
        keep = torch.zeros((self.K,), dtype=torch.long).to(DEVICE)

        cuda_kernel.vote_nms_fast_gpu(pts_score_order, obj_center, 
                                      matching_mat, matching_mat_vote, remv, keep, 
                                      self.dist_thresh, self.vote_thresh)
        keep = keep[keep >= 0] # only select valid index, invalid index marked as -1
        return pts_score_order[keep][:, :3], obj_center, keep
    
class PointNMS_select(nn.Module):
    def __init__(self, point_nms_dic):
        super(PointNMS_select, self).__init__()
        self.point_nms_dic = point_nms_dic
        self.K = self.point_nms_dic['K']
        self.dist_thresh = float(self.point_nms_dic['dist_thresh'])
        self.vote_thresh = float(self.point_nms_dic['vote_thresh'])
        self.score_thresh = float(self.point_nms_dic['score_thresh'])
        if 'grid_size' in self.point_nms_dic.keys():
            self.grid_size = self.point_nms_dic['grid_size']
        if 'pts_range_polar' in self.point_nms_dic.keys():
            self.pts_range_polar = self.point_nms_dic['pts_range_polar']
        if 'pts_range_cart' in self.point_nms_dic.keys():
            self.pts_range_cart = self.point_nms_dic['pts_range_cart']
            
    def forward(self, points, scores, dim='bev'):
        '''
        Inputs:
            points: torch.tensor, [N, 3]
            scores: torch.tensor, [N]
        Outputs:
            obj_center: [K, 3]
            keep: [K]
        '''
        assert points.shape[0] == scores.shape[0], 'points and scores should have the same length'
        assert isinstance(points, torch.Tensor), 'points should be a tensor'
        assert isinstance(scores, torch.Tensor), 'scores should be a tensor'
        if points.dtype != torch.float:
            points = points.float()
        if scores.dtype != torch.float:
            scores = scores.float()
            
        DEVICE = points.device
        pts_score = torch.cat((points, scores.unsqueeze(-1)), dim=-1)
        select_ind = torch.arange(pts_score.shape[0])

        N = pts_score.shape[0]
        _, indices = pts_score[:, -1].sort(dim=0, descending=True)
        select_ind_order = select_ind[indices]
        pts_score_order = pts_score[indices]
        select_ind_order = select_ind_order[pts_score_order[:, -1] > self.score_thresh]
        pts_score_order = pts_score_order[pts_score_order[:, -1] > self.score_thresh]
         
        col_blocks = (N + 64 - 1) // 64
        obj_center = torch.zeros((self.K, 3), dtype=torch.float).to(DEVICE)
        matching_mat = torch.zeros((N, col_blocks), dtype=torch.long).to(DEVICE)
        matching_mat_vote = torch.zeros((N, col_blocks), dtype=torch.long).to(DEVICE)
        remv = torch.zeros((col_blocks,), dtype=torch.long).to(DEVICE)
        keep_order = torch.zeros((self.K,), dtype=torch.long).to(DEVICE)

        if dim == 'polar+height':
            cuda_kernel.vote_nms_fast_gpu(pts_score_order, obj_center, 
                                      matching_mat, matching_mat_vote, remv, keep_order, 
                                      self.dist_thresh, self.vote_thresh)
        elif dim == 'radius':
            rad_score_order = pts_score_order[:, [0, -1]]
            cuda_kernel.vote_nms_fast_gpu(rad_score_order, obj_center, 
                                      matching_mat, matching_mat_vote, remv, keep_order, 
                                      self.dist_thresh, self.vote_thresh)
        elif dim == 'bev':
            assert self.grid_size is not None, 'grid_size should be provided'
            assert self.pts_range_polar is not None, 'pts_range_polar should be provided'
            if not isinstance(self.grid_size, torch.Tensor):
                self.grid_size = torch.tensor(self.grid_size).to(DEVICE)
            self.pts_range_polar = torch.tensor(self.pts_range_polar).to(DEVICE)
            self.pts_range_cart = torch.tensor(self.pts_range_cart).to(DEVICE)
            
            bev_order = pts_score_order[:, :3]/self.grid_size
            bev_order = cartisianize_norm(bev_order, self.pts_range_polar, self.pts_range_cart, return_normed=False)
            bev_order[:, 2] = torch.zeros_like(bev_order[:, 2])
            bev_score_order = torch.cat([bev_order, pts_score_order[:, -1].unsqueeze(-1)], dim=1)
            # print(bev_order.max(axis=0), bev_order.min(axis=0))
            cuda_kernel.vote_nms_fast_gpu(bev_score_order, obj_center, 
                                      matching_mat, matching_mat_vote, remv, keep_order, 
                                      self.dist_thresh, self.vote_thresh)
            
        else:
            raise ValueError('dim should be polar+height, radius or bev')
        # print(f'$$$$$$$ keep_order: {keep_order}')
        keep_order = keep_order[keep_order > -1]  
        return pts_score_order[keep_order][:, :3], obj_center, select_ind_order[keep_order]
    
    
def cartisianize_norm(normed_polar_coor, point_cloud_range_polar, point_cloud_range_cart, return_normed=True):
    """
    Convert normalized polar coordinates to cartisian coordinates.
    Params:
        normed_polar_coors: [N, 3]
        point_cloud_range_polar: polar range of point cloud, [rad_min, ang_min, z_min, rad_max, ang_max, z_max]
        point_cloud_range_cart: cartisian range of point cloud, [x_min, y_min, z_min, x_max, y_max, z_max]
    Returns:
        normed_cat_coors: [N, 3]
    """        
    assert normed_polar_coor.shape[1] == 3, "Polar coordinates must have shape [N, 3]: [r, theta, z]!"
    point_cloud_range_polar = torch.tensor(point_cloud_range_polar, dtype=torch.float).to(normed_polar_coor.device) if isinstance(point_cloud_range_polar, list) else point_cloud_range_polar
    point_cloud_range_cart = torch.tensor(point_cloud_range_cart, dtype=torch.float).to(normed_polar_coor.device) if isinstance(point_cloud_range_cart, list) else point_cloud_range_cart

    polar_coor = normed_polar_coor.new_zeros(normed_polar_coor.shape)
    for i in range(3):
        polar_coor[:, i] = normed_polar_coor[:, i]*(
                            point_cloud_range_polar[i+3] -
                            point_cloud_range_polar[i]) + \
                            point_cloud_range_polar[i]
    x = polar_coor[:, 0] * torch.cos(polar_coor[:, 1])
    y = polar_coor[:, 0] * torch.sin(polar_coor[:, 1])
    cat_coor = torch.stack([x, y, polar_coor[:, 2]], 1)
    
    if not return_normed:
        return cat_coor
    else:
        ############################################################
        # 0. p3f's version
        # normed_cat_coor = cat_coor / (
        #     point_cloud_range[3] - point_cloud_range[0])
        # 1. norm to 0~1, align with (0,0)
        # normed_xy = cat_coor[:, :2] / (point_cloud_range_cart[3:5])
        # 2. norm to -1~1, align with coordinate center
        normed_xy = (cat_coor[:, :2] - point_cloud_range_cart[:2]) / (point_cloud_range_cart[3:5] - point_cloud_range_cart[:2])
        ############################################################

        height = (cat_coor[:, 2] - point_cloud_range_cart[2]) / (point_cloud_range_cart[5] - point_cloud_range_cart[2])
        normed_cart_coor = torch.cat([normed_xy, height[:, None]], 1)
        return normed_cart_coor
    
def fps_sample(pc, num_samples):    
    if isinstance(pc, torch.Tensor):
        pc_input = pc.cpu().numpy().astype(np.float32)
    else:
        pc_input = pc.copy().astype(np.float32)
    if pc_input.shape[0] <= num_samples:
        return pc, torch.arange(pc_input.shape[0]).to(pc.device).long()
    num_samples = int(num_samples)
    # method-1: vanilla fps
    # idx = fpsample.fps_sampling(pc_input, num_samples)
    # method-2: bucket fps
    idx = fpsample.bucket_fps_kdline_sampling(pc_input, num_samples, h=3)
    if isinstance(pc, torch.Tensor):
        idx = torch.from_numpy(idx.astype(np.int64)).to(pc.device).long()
    return pc[idx], idx

def random_sample(pc, num_samples):
    assert isinstance(pc, torch.Tensor), 'input should be a tensor'
    if pc.shape[0] <= num_samples:
        return pc, torch.arange(pc.shape[0]).to(pc.device).long()
    
    idx = torch.randperm(pc.shape[0])[:num_samples]
    return pc[idx], idx
    