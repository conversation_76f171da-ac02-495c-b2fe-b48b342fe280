#!/usr/bin/env python3
"""
测试mask overlap过滤功能
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
import sys
import os

# 添加当前目录到路径
sys.path.append('.')

def create_test_masks():
    """创建测试用的重叠masks"""
    
    # 创建一些重叠的masks
    h, w = 100, 100
    n = 5
    
    masks = torch.zeros((n, h, w), dtype=torch.bool)
    
    # Mask 0: 左上角 (高分数)
    masks[0, 10:40, 10:40] = True
    
    # Mask 1: 与mask 0部分重叠 (低分数)
    masks[1, 25:55, 25:55] = True
    
    # Mask 2: 与mask 1部分重叠 (中等分数)
    masks[2, 40:70, 40:70] = True
    
    # Mask 3: 独立的mask (高分数)
    masks[3, 70:90, 10:30] = True
    
    # Mask 4: 与mask 3高度重叠 (低分数)
    masks[4, 75:95, 15:35] = True
    
    # 对应的classification scores
    class_scores = torch.tensor([0.9, 0.3, 0.6, 0.8, 0.2])
    
    # 对应的类别标签
    obj_labels = ['car', 'car', 'truck', 'bus', 'bus']
    
    return masks, class_scores, obj_labels

def visualize_masks(masks, class_scores, obj_labels, title="Masks"):
    """可视化masks"""
    n = masks.shape[0]
    fig, axes = plt.subplots(1, min(n, 6), figsize=(min(n, 6)*3, 3))
    if n == 1:
        axes = [axes]
    elif n < 6:
        axes = axes[:n]
    
    for i in range(min(n, 6)):
        axes[i].imshow(masks[i].cpu().numpy(), cmap='gray')
        axes[i].set_title(f'Mask {i}\n{obj_labels[i]}\nScore: {class_scores[i]:.3f}')
        axes[i].axis('off')
    
    plt.suptitle(title)
    plt.tight_layout()
    return fig

def calculate_overlap_matrix(masks):
    """计算overlap矩阵"""
    n = masks.shape[0]
    masks_flat = masks.view(n, -1).float()
    
    # 计算intersection和union
    intersections = torch.mm(masks_flat, masks_flat.t())
    areas = masks_flat.sum(dim=1)
    unions = areas.unsqueeze(0) + areas.unsqueeze(1) - intersections
    
    # 计算IoU矩阵
    ious = intersections / (unions + 1e-8)
    
    return ious

def test_overlap_filter():
    """测试overlap过滤功能"""
    
    # 导入过滤函数
    from Grouding_SAM_Main_UDA import filter_overlapping_masks
    
    print("创建测试masks...")
    masks, class_scores, obj_labels = create_test_masks()
    
    print(f"\n原始数据:")
    print(f"Masks: {masks.shape}")
    print(f"Class scores: {class_scores}")
    print(f"Labels: {obj_labels}")
    
    # 计算原始overlap矩阵
    overlap_matrix = calculate_overlap_matrix(masks)
    print(f"\n原始overlap矩阵:")
    for i in range(len(masks)):
        for j in range(len(masks)):
            if i != j and overlap_matrix[i, j] > 0.1:
                print(f"  Mask {i} vs Mask {j}: IoU = {overlap_matrix[i, j]:.3f}")
    
    # 可视化原始masks
    fig1 = visualize_masks(masks, class_scores, obj_labels, "Original Masks")
    plt.savefig("debug_output/original_masks.png", dpi=150, bbox_inches='tight')
    plt.close()
    
    # 测试不同的overlap阈值
    thresholds = [0.1, 0.3, 0.5, 0.7]
    
    for threshold in thresholds:
        print(f"\n{'='*50}")
        print(f"测试 overlap threshold = {threshold}")
        print(f"{'='*50}")
        
        try:
            filtered_masks, filtered_scores, filtered_labels, keep_indices = filter_overlapping_masks(
                masks, class_scores, obj_labels, overlap_threshold=threshold
            )
            
            print(f"\n结果:")
            print(f"保留的masks: {len(filtered_masks)} / {len(masks)}")
            print(f"保留的indices: {keep_indices.tolist()}")
            print(f"保留的labels: {filtered_labels}")
            print(f"保留的scores: {filtered_scores.tolist()}")
            
            # 可视化过滤后的masks
            if len(filtered_masks) > 0:
                fig2 = visualize_masks(filtered_masks, filtered_scores, filtered_labels, 
                                     f"Filtered Masks (threshold={threshold})")
                plt.savefig(f"debug_output/filtered_masks_th{threshold}.png", dpi=150, bbox_inches='tight')
                plt.close()
                
                # 检查过滤后的overlap
                if len(filtered_masks) > 1:
                    filtered_overlap = calculate_overlap_matrix(filtered_masks)
                    print(f"\n过滤后的overlap:")
                    for i in range(len(filtered_masks)):
                        for j in range(len(filtered_masks)):
                            if i != j and filtered_overlap[i, j] > 0.1:
                                print(f"  Mask {i} vs Mask {j}: IoU = {filtered_overlap[i, j]:.3f}")
            
        except Exception as e:
            print(f"错误: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    # 确保输出目录存在
    os.makedirs("debug_output", exist_ok=True)
    
    test_overlap_filter()
    
    print(f"\n测试完成！请查看 debug_output/ 目录中的可视化结果。")
