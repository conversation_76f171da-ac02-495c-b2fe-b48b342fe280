#!/usr/bin/env python3
"""
性能测试和基准测试脚本
"""

import time
import torch
import numpy as np
import psutil
import sys
import os
from tqdm import tqdm

# 添加当前目录到路径
sys.path.append('.')

def benchmark_batch_sizes():
    """测试不同批处理大小的性能"""
    
    print("=== 批处理大小性能测试 ===\n")
    
    batch_sizes = [1, 2, 4, 8, 16]
    results = {}
    
    for batch_size in batch_sizes:
        print(f"测试批处理大小: {batch_size}")
        
        try:
            # 模拟批处理推理
            start_time = time.time()
            
            # 创建模拟数据
            dummy_images = [torch.randn(3, 800, 800) for _ in range(batch_size)]
            
            # 模拟推理时间
            with torch.no_grad():
                for _ in range(10):  # 10次迭代
                    # 模拟GroundingDINO推理
                    time.sleep(0.1 * batch_size)  # 模拟推理时间
                    
                    # 模拟SAM推理
                    time.sleep(0.05 * batch_size)  # 模拟推理时间
            
            end_time = time.time()
            total_time = end_time - start_time
            fps = (batch_size * 10) / total_time
            
            # 记录GPU内存使用
            if torch.cuda.is_available():
                gpu_memory = torch.cuda.memory_allocated() / 1024**3
            else:
                gpu_memory = 0
            
            results[batch_size] = {
                'fps': fps,
                'total_time': total_time,
                'gpu_memory': gpu_memory
            }
            
            print(f"  FPS: {fps:.2f}")
            print(f"  GPU内存: {gpu_memory:.2f}GB")
            print(f"  总时间: {total_time:.2f}s\n")
            
        except Exception as e:
            print(f"  错误: {e}\n")
            results[batch_size] = None
    
    # 输出最佳配置建议
    valid_results = {k: v for k, v in results.items() if v is not None}
    if valid_results:
        best_batch_size = max(valid_results.keys(), key=lambda k: valid_results[k]['fps'])
        print(f"推荐批处理大小: {best_batch_size} (FPS: {valid_results[best_batch_size]['fps']:.2f})")
    
    return results

def benchmark_processing_modes():
    """测试不同处理模式的性能"""
    
    print("=== 处理模式性能测试 ===\n")
    
    modes = ['sequential', 'batch', 'parallel']
    total_frames = 100
    results = {}
    
    for mode in modes:
        print(f"测试处理模式: {mode}")
        
        start_time = time.time()
        
        if mode == 'sequential':
            # 顺序处理
            for i in tqdm(range(total_frames), desc="Sequential"):
                # 模拟单帧处理
                time.sleep(0.01)
        
        elif mode == 'batch':
            # 批处理
            batch_size = 4
            for i in tqdm(range(0, total_frames, batch_size), desc="Batch"):
                # 模拟批处理
                current_batch_size = min(batch_size, total_frames - i)
                time.sleep(0.008 * current_batch_size)
        
        elif mode == 'parallel':
            # 模拟并行处理
            import multiprocessing as mp
            num_processes = min(mp.cpu_count() // 2, 4)
            frames_per_process = total_frames // num_processes
            
            def process_chunk(chunk_size):
                time.sleep(0.01 * chunk_size)
                return chunk_size
            
            with mp.Pool(processes=num_processes) as pool:
                chunks = [frames_per_process] * num_processes
                if total_frames % num_processes:
                    chunks[-1] += total_frames % num_processes
                
                results_parallel = pool.map(process_chunk, chunks)
        
        end_time = time.time()
        total_time = end_time - start_time
        fps = total_frames / total_time
        
        results[mode] = {
            'fps': fps,
            'total_time': total_time
        }
        
        print(f"  FPS: {fps:.2f}")
        print(f"  总时间: {total_time:.2f}s\n")
    
    # 输出最佳模式建议
    best_mode = max(results.keys(), key=lambda k: results[k]['fps'])
    print(f"推荐处理模式: {best_mode} (FPS: {results[best_mode]['fps']:.2f})")
    
    return results

def benchmark_memory_usage():
    """测试内存使用情况"""
    
    print("=== 内存使用测试 ===\n")
    
    # 初始内存状态
    initial_ram = psutil.virtual_memory().percent
    if torch.cuda.is_available():
        initial_gpu = torch.cuda.memory_allocated() / 1024**3
    else:
        initial_gpu = 0
    
    print(f"初始状态:")
    print(f"  RAM使用: {initial_ram:.1f}%")
    print(f"  GPU内存: {initial_gpu:.2f}GB\n")
    
    # 模拟不同负载下的内存使用
    loads = [
        ("轻负载", 1, 100),
        ("中等负载", 4, 400),
        ("重负载", 8, 800)
    ]
    
    for load_name, batch_size, data_size in loads:
        print(f"测试{load_name} (batch_size={batch_size}):")
        
        # 创建模拟数据
        data = []
        for _ in range(batch_size):
            data.append(torch.randn(3, data_size, data_size))
        
        # 移动到GPU（如果可用）
        if torch.cuda.is_available():
            data = [d.cuda() for d in data]
        
        # 记录内存使用
        current_ram = psutil.virtual_memory().percent
        if torch.cuda.is_available():
            current_gpu = torch.cuda.memory_allocated() / 1024**3
        else:
            current_gpu = 0
        
        print(f"  RAM使用: {current_ram:.1f}% (+{current_ram-initial_ram:.1f}%)")
        print(f"  GPU内存: {current_gpu:.2f}GB (+{current_gpu-initial_gpu:.2f}GB)")
        
        # 清理内存
        del data
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        print()

def generate_performance_report():
    """生成性能报告"""
    
    print("=== 系统性能报告 ===\n")
    
    # 系统信息
    print("系统信息:")
    print(f"  CPU核心数: {psutil.cpu_count()}")
    print(f"  总内存: {psutil.virtual_memory().total / 1024**3:.1f}GB")
    print(f"  可用内存: {psutil.virtual_memory().available / 1024**3:.1f}GB")
    
    if torch.cuda.is_available():
        print(f"  GPU设备: {torch.cuda.get_device_name()}")
        print(f"  GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f}GB")
    else:
        print("  GPU: 不可用")
    
    print()
    
    # 性能建议
    total_memory_gb = psutil.virtual_memory().total / 1024**3
    cpu_cores = psutil.cpu_count()
    
    print("性能建议:")
    
    if total_memory_gb >= 64:
        print("  ✅ 内存充足，可以使用大批处理和缓存")
        recommended_batch_size = 8
        recommended_cache = True
    elif total_memory_gb >= 32:
        print("  ⚠️  内存适中，建议中等批处理大小")
        recommended_batch_size = 4
        recommended_cache = True
    else:
        print("  ❌ 内存不足，建议小批处理或顺序处理")
        recommended_batch_size = 2
        recommended_cache = False
    
    if cpu_cores >= 16:
        print("  ✅ CPU核心充足，可以使用并行处理")
        recommended_mode = "parallel"
    elif cpu_cores >= 8:
        print("  ⚠️  CPU核心适中，建议批处理模式")
        recommended_mode = "batch"
    else:
        print("  ❌ CPU核心不足，建议顺序处理")
        recommended_mode = "sequential"
    
    if torch.cuda.is_available():
        gpu_memory_gb = torch.cuda.get_device_properties(0).total_memory / 1024**3
        if gpu_memory_gb >= 16:
            print("  ✅ GPU内存充足，可以使用大批处理和半精度")
        elif gpu_memory_gb >= 8:
            print("  ⚠️  GPU内存适中，建议中等批处理")
        else:
            print("  ❌ GPU内存不足，建议小批处理")
    
    print(f"\n推荐配置:")
    print(f"  处理模式: {recommended_mode}")
    print(f"  批处理大小: {recommended_batch_size}")
    print(f"  使用缓存: {recommended_cache}")

if __name__ == "__main__":
    print("Grouding_SAM_Main_UDA.py 性能基准测试")
    print("=" * 60)
    
    # 运行所有测试
    generate_performance_report()
    print("\n" + "=" * 60)
    
    batch_results = benchmark_batch_sizes()
    print("=" * 60)
    
    mode_results = benchmark_processing_modes()
    print("=" * 60)
    
    benchmark_memory_usage()
    
    print("基准测试完成！")
