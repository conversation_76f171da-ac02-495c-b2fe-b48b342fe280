#!/usr/bin/env python3
"""
测试mask覆盖问题的可视化调试脚本
"""

import numpy as np
import torch
import matplotlib.pyplot as plt
import sys
import os

# 添加tools路径
sys.path.append('tools')
from general_tool import compress_mask

def create_test_masks():
    """创建测试用的重叠masks"""
    h, w = 100, 100
    n = 12  # 12个mask，模拟你的情况
    
    masks = np.zeros((n, h, w), dtype=bool)
    
    # 创建一些重叠的masks
    # Mask 0: 左上角
    masks[0, 10:40, 10:40] = True
    
    # Mask 1: 与mask 0部分重叠
    masks[1, 30:50, 30:50] = True
    
    # Mask 2: 与mask 1部分重叠
    masks[2, 45:65, 45:65] = True
    
    # Mask 3: 大面积，与前面多个重叠
    masks[3, 20:70, 20:70] = True
    
    # Mask 4-11: 其他位置的小mask
    for i in range(4, 12):
        x = (i-4) * 10 + 5
        y = 70
        masks[i, y:y+15, x:x+15] = True
    
    return masks

def main():
    print("创建测试masks...")
    test_masks = create_test_masks()
    
    print(f"测试masks形状: {test_masks.shape}")
    print(f"每个mask的像素数:")
    for i in range(test_masks.shape[0]):
        print(f"  Mask {i}: {test_masks[i].sum()} pixels")
    
    # 测试compress_mask函数
    print("\n开始测试compress_mask...")
    debug_path = "debug_output/test_mask_debug.png"
    
    # 确保输出目录存在
    os.makedirs("debug_output", exist_ok=True)
    
    result = compress_mask(test_masks, scale_factor=0.4, debug_save_path=debug_path)
    
    print(f"\n结果:")
    print(f"输入: {test_masks.shape[0]} masks")
    print(f"输出unique labels: {np.unique(result)}")
    print(f"缺失的labels: {[i+1 for i in range(test_masks.shape[0]) if (i+1) not in np.unique(result)]}")
    
    print(f"\n调试图片已保存到: {debug_path}")
    print("请查看以下文件:")
    print(f"  - {debug_path.replace('.png', '_original_masks.png')}")
    print(f"  - {debug_path.replace('.png', '_step_XX.png')} (每个处理步骤)")
    print(f"  - {debug_path.replace('.png', '_final_analysis.png')}")
    print(f"  - {debug_path.replace('.png', '_overlap_stats.txt')}")

if __name__ == "__main__":
    main()
