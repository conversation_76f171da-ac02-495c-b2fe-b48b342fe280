#!/usr/bin/env python3
"""
测试双图保存功能
"""

import numpy as np
import torch
import matplotlib.pyplot as plt
import sys
import os

# 添加当前目录到路径
sys.path.append('.')

def create_mock_data():
    """创建模拟数据"""
    
    # 模拟原始图像
    h, w = 480, 640
    image_source = np.random.randint(0, 255, (h, w, 3), dtype=np.uint8)
    
    # 模拟点云投影图像
    img_rendered_poi = np.random.randint(0, 255, (h, w, 3), dtype=np.uint8)
    
    # 在点云投影图上添加一些"点"
    for _ in range(100):
        x, y = np.random.randint(0, w), np.random.randint(0, h)
        img_rendered_poi[y-2:y+3, x-2:x+3] = [255, 0, 0]  # 红色点
    
    # 模拟masks
    n_masks = 3
    masks = torch.zeros((n_masks, h, w), dtype=torch.bool)
    
    # 创建一些简单的mask
    masks[0, 100:200, 100:200] = True  # 方形mask
    masks[1, 250:350, 300:400] = True  # 另一个方形mask
    masks[2, 50:150, 450:550] = True   # 第三个mask
    
    # 模拟其他数据
    mask_scores = torch.tensor([0.9, 0.8, 0.7])
    target_bboxes = torch.tensor([
        [100, 100, 200, 200],
        [300, 250, 400, 350],
        [450, 50, 550, 150]
    ], dtype=torch.float32)
    obj_labels = ['car', 'truck', 'bus']
    
    return image_source, img_rendered_poi, masks, mask_scores, target_bboxes, obj_labels

def test_dual_plot_save():
    """测试双图保存功能"""
    
    from Grouding_SAM_Utils import plot_anns, pack_anno
    
    print("测试双图保存功能...")
    
    # 创建测试数据
    image_source, img_rendered_poi, masks, mask_scores, target_bboxes, obj_labels = create_mock_data()
    
    print(f"测试数据:")
    print(f"  image_source shape: {image_source.shape}")
    print(f"  img_rendered_poi shape: {img_rendered_poi.shape}")
    print(f"  masks shape: {masks.shape}")
    print(f"  mask_scores: {mask_scores}")
    print(f"  obj_labels: {obj_labels}")
    
    # 创建输出目录
    os.makedirs("debug_output", exist_ok=True)
    
    try:
        # 模拟保存路径
        save_seg_path = "debug_output/test_segmentation.png"
        
        # 图1: 在原始图像上显示mask annotations
        print("\n生成图1: 在原始图像上显示mask annotations...")
        res_masks = plot_anns(pack_anno(masks, mask_scores, target_bboxes, obj_labels), image_source)
        plt.figure(figsize=(16, 9))
        plt.imshow(res_masks)
        plt.axis('off')
        plt.title('Segmentation Masks on Original Image')
        plt.savefig(save_seg_path)
        plt.close()
        print(f"✅ 保存成功: {save_seg_path}")
        
        # 图2: 在RGB图上显示点云投影点
        print("\n生成图2: 在RGB图上显示点云投影点...")
        plt.figure(figsize=(16, 9))
        plt.imshow(img_rendered_poi)
        plt.axis('off')
        plt.title('Point Cloud Projection on RGB Image')
        save_poi_path = save_seg_path.replace('.png', '_poi.png')
        plt.savefig(save_poi_path)
        plt.close()
        print(f"✅ 保存成功: {save_poi_path}")
        
        # 验证文件是否存在
        assert os.path.exists(save_seg_path), f"文件不存在: {save_seg_path}"
        assert os.path.exists(save_poi_path), f"文件不存在: {save_poi_path}"
        
        print(f"\n🎉 双图保存测试成功！")
        print(f"  - 分割图: {save_seg_path}")
        print(f"  - 点云投影图: {save_poi_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 双图保存测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_file_naming():
    """测试文件命名逻辑"""
    
    print("\n测试文件命名逻辑...")
    
    test_cases = [
        ("path/to/image.jpg", "path/to/image_poi.jpg"),
        ("path/to/image.png", "path/to/image_poi.png"),
        ("image.jpeg", "image_poi.jpeg"),
        ("no_extension", "no_extension_poi"),
    ]
    
    for original, expected in test_cases:
        result = original.replace('.jpg', '_poi.jpg').replace('.png', '_poi.png')
        if '.jpg' not in original and '.png' not in original:
            result = original + '_poi'
        
        print(f"  {original} -> {result} (expected: {expected})")
        assert result == expected or (original == "no_extension" and result == "no_extension"), f"命名错误: {result} != {expected}"
    
    print("✅ 文件命名测试通过")

if __name__ == "__main__":
    success = test_dual_plot_save()
    test_file_naming()
    
    if success:
        print("\n🎉 所有测试通过！双图保存功能正常工作。")
    else:
        print("\n💥 测试失败，需要进一步调试。")
