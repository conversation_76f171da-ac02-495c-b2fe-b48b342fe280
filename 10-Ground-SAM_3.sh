# # for Grounding-DINO+SAM
# # move main.py to tools/Grounded-Segment-Anything
# cp Grouding_SAM_Main.py tools/Grounded-Segment-Anything/
# cp Grouding_SAM_Utils.py tools/Grounded-Segment-Anything/

# conda activate dino
# CUDA_VISIBLE_DEVICES=2 \
# python Grouding_SAM_Postprocess_HTC.py --data_mode trainval --dset val --part '7/8' --model maskrcnn --use_bbox 

CUDA_VISIBLE_DEVICES=2 \
python Grouding_SAM_Main_UDA.py --data_mode trainval --dset val_night --part '3/8'