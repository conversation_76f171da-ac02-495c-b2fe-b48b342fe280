#!/usr/bin/env python3
"""
COCO格式保存和加载的使用示例
"""

import torch
import numpy as np
import sys
import os

# 添加当前目录到路径
sys.path.append('.')

def example_save_and_load():
    """演示如何保存和加载COCO格式数据"""
    
    from Grouding_SAM_Main_UDA import save_masks_and_annotations_coco_format, load_masks_and_annotations_coco_format
    
    print("=== COCO格式保存和加载示例 ===\n")
    
    # 1. 创建示例数据
    print("1. 创建示例数据...")
    
    # 创建masks
    h, w = 240, 320  # 较小的尺寸用于演示
    masks = torch.zeros((2, h, w), dtype=torch.bool)
    masks[0, 50:100, 50:100] = True  # 第一个mask
    masks[1, 150:200, 200:250] = True  # 第二个mask
    
    # 创建scores和其他数据
    class_scores = torch.tensor([0.95, 0.87])
    mask_scores = torch.tensor([[0.92], [0.81]])
    bboxes = torch.tensor([[50, 50, 100, 100], [200, 150, 250, 200]], dtype=torch.float32)
    obj_labels = ['car', 'truck']
    thing_texts = ['car', 'truck', 'bus', 'traffic-cone']
    
    print(f"  - Masks shape: {masks.shape}")
    print(f"  - Class scores: {class_scores.tolist()}")
    print(f"  - Object labels: {obj_labels}")
    
    # 2. 保存数据
    print("\n2. 保存数据为COCO格式...")
    
    os.makedirs("example_output", exist_ok=True)
    save_path = "example_output/sample_data.pth"
    
    save_masks_and_annotations_coco_format(
        masks, class_scores, mask_scores, bboxes, obj_labels,
        save_path, thing_texts, scale_factor=1.0  # 不缩放，保持原始尺寸
    )
    
    # 3. 加载数据
    print("\n3. 加载COCO格式数据...")
    
    loaded_masks, loaded_class_scores, loaded_mask_scores, loaded_bboxes, loaded_obj_labels, coco_data = load_masks_and_annotations_coco_format(save_path)
    
    print(f"  - 加载的masks shape: {loaded_masks.shape}")
    print(f"  - 加载的class scores: {loaded_class_scores.tolist()}")
    print(f"  - 加载的object labels: {loaded_obj_labels}")
    
    # 4. 验证数据一致性
    print("\n4. 验证数据一致性...")
    
    masks_match = torch.equal(masks, loaded_masks)
    scores_match = torch.allclose(class_scores, loaded_class_scores)
    labels_match = obj_labels == loaded_obj_labels
    
    print(f"  - Masks匹配: {masks_match}")
    print(f"  - Scores匹配: {scores_match}")
    print(f"  - Labels匹配: {labels_match}")
    
    if masks_match and scores_match and labels_match:
        print("✅ 所有数据完全匹配！")
    else:
        print("❌ 数据不匹配！")
        return False
    
    # 5. 显示COCO数据结构
    print("\n5. COCO数据结构:")
    print(f"  - 图像信息: {coco_data['images'][0]}")
    print(f"  - 类别数量: {len(coco_data['categories'])}")
    print(f"  - 标注数量: {len(coco_data['annotations'])}")
    
    for i, ann in enumerate(coco_data['annotations']):
        print(f"  - 标注 {i+1}: ID={ann['id']}, 类别={ann['class_name']}, "
              f"类别分数={ann['class_score']:.3f}, mask分数={ann['mask_score']:.3f}")
    
    return True

def example_direct_file_loading():
    """演示直接通过文件路径加载数据"""
    
    from Grouding_SAM_Main_UDA import load_masks_and_annotations_coco_format_by_files
    
    print("\n=== 直接文件加载示例 ===\n")
    
    mask_path = "example_output/sample_data_masks.png"
    json_path = "example_output/sample_data_annotations.json"
    
    if not (os.path.exists(mask_path) and os.path.exists(json_path)):
        print("❌ 示例文件不存在，请先运行保存示例")
        return False
    
    try:
        # 直接加载文件
        masks, class_scores, mask_scores, bboxes, obj_labels, coco_data = load_masks_and_annotations_coco_format_by_files(mask_path, json_path)
        
        print(f"直接加载结果:")
        print(f"  - Masks shape: {masks.shape}")
        print(f"  - Class scores: {class_scores.tolist()}")
        print(f"  - Object labels: {obj_labels}")
        
        return True
        
    except Exception as e:
        print(f"❌ 直接加载失败: {e}")
        return False

def example_usage_in_pipeline():
    """演示在处理流水线中的使用方法"""
    
    print("\n=== 流水线使用示例 ===\n")
    
    print("在主处理流程中，你可以这样使用：")
    print("""
# 保存数据（替换原来的torch.save）
save_masks_and_annotations_coco_format(
    masks_, det_scores, mask_scores, target_bboxes, obj_labels, 
    save_pth_path, thing_texts, scale_factor=0.4
)

# 加载数据（用于后续处理或分析）
masks, class_scores, mask_scores, bboxes, obj_labels, coco_data = load_masks_and_annotations_coco_format(save_pth_path)

# 或者直接通过文件路径加载
mask_path = "path/to/masks.png"
json_path = "path/to/annotations.json"
masks, class_scores, mask_scores, bboxes, obj_labels, coco_data = load_masks_and_annotations_coco_format_by_files(mask_path, json_path)
""")
    
    print("优势：")
    print("  ✅ Mask以图像格式保存，便于可视化和处理")
    print("  ✅ 标注以标准COCO JSON格式保存，便于与其他工具集成")
    print("  ✅ 支持类别分数和mask质量分数")
    print("  ✅ 完全可逆的保存和加载过程")
    print("  ✅ 节省存储空间（相比原始torch.save）")

if __name__ == "__main__":
    success1 = example_save_and_load()
    success2 = example_direct_file_loading()
    example_usage_in_pipeline()
    
    if success1 and success2:
        print("\n🎉 所有示例运行成功！")
        print("生成的文件：")
        print("  - example_output/sample_data_masks.png")
        print("  - example_output/sample_data_annotations.json")
    else:
        print("\n💥 部分示例失败。")
