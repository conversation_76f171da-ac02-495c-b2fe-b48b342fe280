import io
import torch
import numpy as np
from typing import Tuple
import requests
from PIL import Image, ImageDraw, ImageFont
import matplotlib.pyplot as plt
from torchvision.ops.boxes import batched_nms

from huggingface_hub import hf_hub_download
from GroundingDINO.groundingdino.models import build_model
from GroundingDINO.groundingdino.util.slconfig import SLConfig
from GroundingDINO.groundingdino.util.utils import clean_state_dict, get_phrases_from_posmap
import GroundingDINO.groundingdino.datasets.transforms as T

def lift_instance_mask(instance_mask, include_background=False):
    """
    将 [H, W] 的 instance mask 提升为 [N, H, W] 的 binary mask。

    Args:
        instance_mask (ndarray): [H, W] 的 numpy array，每个像素为 instance id。
        include_background (bool): 是否包含背景（id==0）。

    Returns:
        binary_masks (ndarray): [N, H, W] 的 binary array，第 i 个 channel 对应第 i 个 instance。
        instance_ids (ndarray): [N] 的 array，对应每个 channel 的 instance id。
    """
    unique_ids = np.unique(instance_mask)
    if not include_background:
        unique_ids = unique_ids[unique_ids != 0]

    binary_masks = (instance_mask[None, :, :] == unique_ids[:, None, None]).astype(np.uint8)
    return binary_masks, unique_ids


def load_image_from_np(image:np.array) -> Tuple[np.array, torch.Tensor]:
    transform = T.Compose(
        [
            T.RandomResize([800], max_size=1333),
            T.ToTensor(),
            T.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225]),
        ]
    )
    image = image[:, :, [2, 1, 0]] # BGR to RGB
    image_source = Image.fromarray(image)
    # image = np.asarray(image_source)
    image_transformed, _ = transform(image_source, None)
    return image, image_transformed

def load_model_hf(repo_id, filename, ckpt_config_filename, device='cpu'):
    cache_config_file = hf_hub_download(repo_id=repo_id, filename=ckpt_config_filename)

    args = SLConfig.fromfile(cache_config_file) 
    model = build_model(args)
    args.device = device

    cache_file = hf_hub_download(repo_id=repo_id, filename=filename)
    checkpoint = torch.load(cache_file, map_location='cpu')
    log = model.load_state_dict(clean_state_dict(checkpoint['model']), strict=False)
    print("Model loaded from {} \n => {}".format(cache_file, log))
    _ = model.eval()
    return model   

def download_image(url, image_file_path):
    r = requests.get(url, timeout=4.0)
    if r.status_code != requests.codes.ok:
        assert False, 'Status code error: {}.'.format(r.status_code)

    with Image.open(io.BytesIO(r.content)) as im:
        im.save(image_file_path)

    print('Image downloaded from url: {} and saved to: {}.'.format(url, image_file_path))

def plot_anns(anns, image):
    """show all masks on the image"""
    if len(anns) == 0:
        return
    sorted_anns = sorted(anns, key=(lambda x: x['area']), reverse=True)
    # ax = plt.gca()
    # ax.set_autoscale_on(False)

    img = np.ones((sorted_anns[0]['segmentation'].shape[0], sorted_anns[0]['segmentation'].shape[1], 4))
    img[:,:,3] = 0
    for ann in sorted_anns:
        m = ann['segmentation']
        color_mask = np.concatenate([np.random.random(3), [0.35]])
        img[m] = color_mask
    # ax.imshow(img)
    # add the image to the plot
    annotated_frame_pil = Image.fromarray(image).convert("RGBA")
    mask_image_pil = Image.fromarray((img * 255).astype(np.uint8)).convert("RGBA")
    return np.array(Image.alpha_composite(annotated_frame_pil, mask_image_pil))
    
    
def show_all_mask(masks, scores, boxes, img):
    """show every mask on the image one by one"""
    for i, (mask, score) in enumerate(zip(masks, scores)):
        plt.figure(figsize=(10,10))
        plt.imshow(img)
        _show_mask(mask, plt.gca())
        # _show_box(boxes[i], plt.gca())
        # plt.title(f"Mask {i+1}, Score: {score:.3f}", fontsize=18)
        plt.axis('off')
        plt.show()  
  
def _show_mask(mask, ax, random_color=False):
    if random_color:
        color = np.concatenate([np.random.random(3), np.array([0.6])], axis=0)
    else:
        color = np.array([30/255, 144/255, 255/255, 0.6])
    h, w = mask.shape[-2:]
    if mask.device.type == 'cuda':
        mask = mask.cpu()
    mask_image = mask.reshape(h, w, 1) * color.reshape(1, 1, -1)
    ax.imshow(mask_image)
    
def _show_box(box, ax):
    x0, y0 = box[0], box[1]
    w, h = box[2] - box[0], box[3] - box[1]
    ax.add_patch(plt.Rectangle((x0, y0), w, h, edgecolor='green', facecolor=(0,0,0,0), lw=2))    

def pack_anno(masks, scores, target_bboxes, obj_labels):
    anno = []
    for i, (mask, score, box, label) in enumerate(zip(masks, scores, target_bboxes, obj_labels)):
        anno.append({
            'segmentation': mask.cpu().numpy(),
            'scores': score.item(),
            'boxes': box.cpu().numpy(),
            'area': mask.sum().item(),
            'label': label
        })
    return anno


def keep_overlaped(boxes1, boxes2, logits1, logits2, phrases1, phrases2, threshold):
    assert boxes1.shape[1] == 4
    assert boxes2.shape[1] == 4
    if boxes1.shape[0] == 0 or boxes2.shape[0] == 0:
        return torch.tensor([]), torch.tensor([]), []
    
    results_box, results_logit, results_phrase = [], [], []

    for i in range(boxes1.size(0)):
        x1, y1, w1, h1 = boxes1[i]
        x2, y2 = x1 + w1, y1 + h1
        
        x1_prime = boxes2[:, 0]
        y1_prime = boxes2[:, 1]
        w2_prime = boxes2[:, 2]
        h2_prime = boxes2[:, 3]
        x2_prime = x1_prime + w2_prime
        y2_prime = y1_prime + h2_prime
        
        inter_x1 = torch.max(x1, x1_prime)
        inter_y1 = torch.max(y1, y1_prime)
        inter_x2 = torch.min(x2, x2_prime)
        inter_y2 = torch.min(y2, y2_prime)
        
        inter_w = torch.clamp(inter_x2 - inter_x1, min=0)
        inter_h = torch.clamp(inter_y2 - inter_y1, min=0)
        
        inter_area = inter_w * inter_h
        area1 = w1 * h1
        area2 = w2_prime * h2_prime
        
        iou = inter_area / (area1 + area2 - inter_area)
        
        valid_mask = iou > threshold
        
        if valid_mask.any():
            valid_indices = torch.nonzero(valid_mask, as_tuple=False).squeeze()
            if valid_indices.dim() == 0:
                valid_indices = valid_indices.unsqueeze(0)
            
            for idx in valid_indices:
                if logits1[i] >= logits2[idx]:
                    selected_box = boxes1[i]
                    selected_logit = logits1[i]
                    selected_phrase = phrases1[i]
                else:
                    selected_box = boxes2[idx]
                    selected_logit = logits2[idx]
                    selected_phrase = phrases2[idx]
                
                results_box.append(selected_box.unsqueeze(0))
                results_logit.append(selected_logit)
                results_phrase.append(selected_phrase)
    if len(results_box) == 0:
        return torch.tensor([]), torch.tensor([]), []
    results_box = torch.cat(results_box, dim=0)
    results_logit = torch.tensor(results_logit)
    return results_box, results_logit, results_phrase

def filter_boxes(boxes, logits, phrases, thing_texts, thre=0.7):
    '''filter out boxes that are in thing_texts by nms'''
    assert boxes.shape[0] == logits.shape[0] == len(phrases)

    # 处理嵌套列表的情况
    if isinstance(phrases[0], list):
        print(f"Warning: phrases is nested list: {phrases}")
        phrases = [item[0] if isinstance(item, list) and len(item) > 0 else str(item) for item in phrases]
        print(f"Flattened phrases: {phrases}")

    # 确保phrases中的每个元素都是字符串
    phrases = [str(phrase) for phrase in phrases]

    phrase = phrases[0].replace(' - ', '-') # avoid 'xxx - x' occur
    if ' ' in phrase:
        phrase = phrase.replace(' ', '-') # avoid 'xxx x' occur
    if phrase=='safty-cone' or phrase=='traffic' or phrase=='cone':
        phrase = 'traffic-cone'
    try:
        d = thing_texts.index(phrase)
    except:
        d = 3 # error occurs when the phrase is 'traffic-cone'
    phrases_id = torch.ones_like(boxes[:, 0])*d
    
    keep_by_nms = batched_nms(
                boxes,
                logits,
                phrases_id,
                iou_threshold=thre,
            )
    # print(keep_by_nms)
    phrases_ = []
    for i in range(phrases_id[keep_by_nms].shape[0]):
        phrases_.append(thing_texts[int(phrases_id[i])])
    
    assert boxes[keep_by_nms].shape[0] == logits[keep_by_nms].shape[0] == len(phrases_)
    return boxes[keep_by_nms], logits[keep_by_nms], phrases_, keep_by_nms

def plot_point_in_camview(points, coloring, im, dot_size=5):
    import matplotlib.pyplot as plt
    import numpy as np
    import io

    # 设置非交互式后端
    plt.ioff()

    fig, ax = plt.subplots(1, 1, figsize=(9, 16))
    fig.canvas.set_window_title('Point cloud in camera view')

    ax.imshow(im)

    # 处理points的格式：原始函数期望 (2, N)，我们的调用传入 (2, N)
    
    ax.scatter(points[0, :], points[1, :], c=coloring, s=dot_size)

    ax.axis('off')

    # 将figure转换为numpy array
    fig.canvas.draw()

    # 使用canvas.tostring_rgb()
    buf = fig.canvas.tostring_rgb()
    ncols, nrows = fig.canvas.get_width_height()
    img_array = np.frombuffer(buf, dtype=np.uint8).reshape(nrows, ncols, 3)

    plt.close(fig)  # 释放内存

    return img_array