#!/usr/bin/env python3
"""
测试 filter_boxes 函数的修复效果
"""

import torch
import numpy as np
from Grouding_SAM_Utils import filter_boxes

def create_test_data():
    """创建测试数据：包含重叠的boxes"""
    
    # 创建一些重叠的boxes (xyxy格式)
    boxes = torch.tensor([
        [10, 10, 50, 50],    # box 0: car
        [15, 15, 55, 55],    # box 1: car (与box 0重叠)
        [100, 100, 140, 140], # box 2: truck
        [105, 105, 145, 145], # box 3: truck (与box 2重叠)
        [200, 200, 240, 240], # box 4: car
        [12, 12, 52, 52],    # box 5: car (与box 0高度重叠)
    ], dtype=torch.float32)
    
    # 对应的confidence scores
    logits = torch.tensor([0.9, 0.8, 0.85, 0.75, 0.95, 0.7])
    
    # 对应的phrases
    phrases = ['car', 'car', 'truck', 'truck', 'car', 'car']
    
    # thing_texts
    thing_texts = ['car', 'truck', 'bus', 'traffic-cone']
    
    return boxes, logits, phrases, thing_texts

def calculate_iou(box1, box2):
    """计算两个box的IoU"""
    x1 = max(box1[0], box2[0])
    y1 = max(box1[1], box2[1])
    x2 = min(box1[2], box2[2])
    y2 = min(box1[3], box2[3])
    
    if x2 <= x1 or y2 <= y1:
        return 0.0
    
    intersection = (x2 - x1) * (y2 - y1)
    area1 = (box1[2] - box1[0]) * (box1[3] - box1[1])
    area2 = (box2[2] - box2[0]) * (box2[3] - box2[1])
    union = area1 + area2 - intersection
    
    return intersection / union

def test_filter_boxes():
    """测试filter_boxes函数"""
    
    print("创建测试数据...")
    boxes, logits, phrases, thing_texts = create_test_data()
    
    print(f"\n原始数据:")
    print(f"Boxes: {boxes.shape}")
    print(f"Logits: {logits}")
    print(f"Phrases: {phrases}")
    print(f"Thing texts: {thing_texts}")
    
    # 计算原始boxes之间的IoU
    print(f"\n原始boxes之间的IoU:")
    for i in range(len(boxes)):
        for j in range(i+1, len(boxes)):
            iou = calculate_iou(boxes[i], boxes[j])
            if iou > 0:
                print(f"  Box {i} ({phrases[i]}) vs Box {j} ({phrases[j]}): IoU = {iou:.3f}")
    
    # 测试不同的IoU阈值
    thresholds = [0.1, 0.3, 0.5, 0.7]
    
    for thre in thresholds:
        print(f"\n{'='*50}")
        print(f"测试 IoU threshold = {thre}")
        print(f"{'='*50}")
        
        try:
            filtered_boxes, filtered_logits, filtered_phrases, keep_indices = filter_boxes(
                boxes, logits, phrases, thing_texts, thre=thre
            )
            
            print(f"\n结果:")
            print(f"保留的boxes: {len(filtered_boxes)} / {len(boxes)}")
            print(f"保留的indices: {keep_indices.tolist()}")
            print(f"保留的phrases: {filtered_phrases}")
            print(f"保留的scores: {filtered_logits.tolist()}")
            
            # 检查保留的boxes之间的IoU
            print(f"\n保留boxes之间的IoU:")
            for i in range(len(filtered_boxes)):
                for j in range(i+1, len(filtered_boxes)):
                    iou = calculate_iou(filtered_boxes[i], filtered_boxes[j])
                    if iou > 0:
                        print(f"  Box {i} ({filtered_phrases[i]}) vs Box {j} ({filtered_phrases[j]}): IoU = {iou:.3f}")
            
        except Exception as e:
            print(f"错误: {e}")

if __name__ == "__main__":
    test_filter_boxes()
