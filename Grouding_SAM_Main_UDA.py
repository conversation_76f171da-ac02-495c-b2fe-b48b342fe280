import os
import argparse
import sys
import copy
import numpy as np
import torch
import argparse
from PIL import Image, ImageDraw, ImageFont
from torchvision.ops import box_convert
import pickle
from tqdm import tqdm
from nuscenes.nuscenes import NuScenes
import h5py

# Grounding DINO
sys.path.append('tool-dino/Grounded-Segment-Anything')
import GroundingDINO.groundingdino.datasets.transforms as T
from GroundingDINO.groundingdino.models import build_model
from GroundingDINO.groundingdino.util import box_ops
from GroundingDINO.groundingdino.util.slconfig import SLConfig
from GroundingDINO.groundingdino.util.utils import clean_state_dict, get_phrases_from_posmap
from GroundingDINO.groundingdino.util.inference import annotate, load_image, predict
from huggingface_hub import hf_hub_download

# segment anything
try:    
    from segment_anything.segment_anything import build_sam, SamPredictor 
except:
    from segment_anything import build_sam, SamPredictor 
import cv2
import numpy as np
import matplotlib.pyplot as plt
from Grouding_SAM_Utils import load_image_from_np, load_model_hf, plot_anns, pack_anno, keep_overlaped, filter_boxes

from sklearn.cluster import DBSCAN
from sklearn.metrics import fowlkes_mallows_score
from sklearn.metrics import homogeneity_score, completeness_score, v_measure_score

# load pkl
sys.path.append('tools')
from pkl_utils import load_viewimages_path, load_point_mask_viewimages
from general_tool import load_point, load_mask, load_viewimages, compress_mask, expand_mask, expand_mask_torch
from projection.pc2img import map_pointcloud_to_image_2, plot_point_in_camview # , plot_point_in_camview_ret 
from cluster_utils import get_comfusion_matrix
from heatmap_convertor import cand_recall_calc

def filter_overlapping_masks(masks, class_scores, obj_labels, overlap_threshold=0.5):
    """
    过滤重叠的masks，当overlap大于阈值时保留class_score更高的mask

    Args:
        masks (torch.Tensor): 形状为 (N, H, W) 的二值mask
        class_scores (torch.Tensor): 形状为 (N,) 的分类分数
        obj_labels (list): 长度为 N 的类别标签列表
        overlap_threshold (float): overlap阈值，默认0.5

    Returns:
        tuple: (filtered_masks, filtered_scores, filtered_labels, keep_indices)
    """
    import torch

    n = masks.shape[0]
    if n <= 1:
        return masks, class_scores, obj_labels, torch.arange(n)


    # 计算所有mask pairs的overlap
    masks_flat = masks.view(n, -1).float()  # (N, H*W)

    # 计算intersection和union
    intersections = torch.mm(masks_flat, masks_flat.t())  # (N, N)
    areas = masks_flat.sum(dim=1)  # (N,)
    unions = areas.unsqueeze(0) + areas.unsqueeze(1) - intersections  # (N, N)

    # 计算IoU矩阵，避免除零
    ious = intersections / (unions + 1e-8)

    # 只考虑上三角矩阵（避免重复计算）
    triu_mask = torch.triu(torch.ones_like(ious, dtype=torch.bool), diagonal=1)
    overlapping_pairs = torch.where((ious > overlap_threshold) & triu_mask)


    # 记录需要移除的mask索引
    to_remove = set()

    for i, j in zip(overlapping_pairs[0], overlapping_pairs[1]):
        i, j = i.item(), j.item()

        # 如果两个mask都还没被标记为移除
        if i not in to_remove and j not in to_remove:
            overlap_ratio = ious[i, j].item()
            score_i = class_scores[i].item()
            score_j = class_scores[j].item()
            # 保留分数更高的mask
            if score_i >= score_j:
                to_remove.add(j)
            else:
                to_remove.add(i)

    # 创建保留的索引
    keep_indices = torch.tensor([i for i in range(n) if i not in to_remove], dtype=torch.long)


    if len(keep_indices) == 0:
        # 如果所有mask都被移除，至少保留分数最高的一个
        best_idx = torch.argmax(class_scores)
        keep_indices = torch.tensor([best_idx])

    # 过滤结果
    filtered_masks = masks[keep_indices]
    filtered_scores = class_scores[keep_indices]
    filtered_labels = [obj_labels[i] for i in keep_indices]

    return filtered_masks, filtered_scores, filtered_labels, keep_indices

def get_detailed_classification_scores(model, image, caption, box_threshold, text_threshold, device="cuda"):
    """
    获取详细的classification scores，包括每个token的分数

    Returns:
        boxes: 检测框
        max_scores: 每个框的最大分数
        all_scores: 每个框对所有token的分数 (n, 256)
        phrases: 检测到的短语
    """
    from GroundingDINO.groundingdino.util.inference import preprocess_caption
    from GroundingDINO.groundingdino.util.utils import get_phrases_from_posmap

    caption = preprocess_caption(caption=caption)
    model = model.to(device)
    image = image.to(device)

    with torch.no_grad():
        outputs = model(image[None], captions=[caption])

    prediction_logits = outputs["pred_logits"].cpu().sigmoid()[0]  # (nq, 256)
    prediction_boxes = outputs["pred_boxes"].cpu()[0]  # (nq, 4)

    mask = prediction_logits.max(dim=1)[0] > box_threshold
    all_scores = prediction_logits[mask]  # (n, 256) - 所有token的分数
    boxes = prediction_boxes[mask]  # (n, 4)
    max_scores = all_scores.max(dim=1)[0]  # (n,) - 每个框的最大分数

    tokenizer = model.tokenizer
    tokenized = tokenizer(caption)

    phrases = [
        get_phrases_from_posmap(logit > text_threshold, tokenized, tokenizer).replace('.', '')
        for logit in all_scores
    ]

    return boxes, max_scores, all_scores, phrases

# def filter_3d_points_in_mask_optimized(points_3d, poi, sam_mask, view_mask):
#     """优化版本：使用向量化操作"""
#     poi = poi.round().to(torch.int64)
#     n, h, w = sam_mask.shape
#     N = points_3d.shape[0]
#     sel_idx = torch.arange(0, N, device=poi.device)[view_mask]
    
#     # 向量化处理所有masks
#     mask_values = sam_mask[:, poi[:, 1], poi[:, 0]]  # shape: (n, P)
    
#     valid_indices = []
#     for i in range(n):
#         mask_indices = mask_values[i] == 1
#         valid_indices.append(sel_idx[mask_indices])
    
#     return valid_indices

def filter_3d_points_in_mask_optimized(points_3d, poi, sam_mask, view_mask, lidar_idx, sem_mask):
    """
    优化版本：筛选落在 2D mask 中的 3D 点，使用向量化操作提高效率

    参数:
        points_3d (torch.Tensor): 形状为 (P, 3/4) 的已过滤3D点云坐标 (已经通过view_mask过滤)
        poi (torch.Tensor): 形状为 (P, 2) 的投影点云坐标 (x, y)
        sam_mask (torch.Tensor): 形状为 (n, H, W) 的二值化 mask 图像
        view_mask (torch.Tensor): 形状为 (N,) 的布尔掩码，表示哪些点在当前视角可见
        lidar_idx (torch.Tensor): 形状为 (N,) 的激光雷达点索引
        sem_mask (np.ndarray): 形状为 (N,) 的语义掩码

    返回:
        tuple: (obj_poi_coors, obj_poi_idx, obj_poi_labels) 每个都是长度为n的列表
    """
    # 确保数据类型正确
    poi = poi.round().to(torch.int64)
    n, h, w = sam_mask.shape
    P = points_3d.shape[0]  # 已过滤点的数量

    # points_3d, poi 已经是经过view_mask过滤的数据，直接使用
    # 获取对应的lidar_idx和sem_mask (需要用view_mask过滤原始数据)
    visible_lidar_idx = lidar_idx[view_mask]  # (P,)
    visible_sem_mask = torch.tensor(sem_mask[view_mask]).to(points_3d.device)  # (P,)

    # 确保数据类型一致
    visible_lidar_idx = visible_lidar_idx.to(torch.int64)
    visible_sem_mask = visible_sem_mask.to(torch.int64)
    h, w = sam_mask.shape[1], sam_mask.shape[2]

    # 更严格的边界检查
    poi[:, 0] = torch.clamp(poi[:, 0], 0, w-1)  # x坐标
    poi[:, 1] = torch.clamp(poi[:, 1], 0, h-1)  # y坐标
    valid_poi_mask = (poi[:, 0] >= 0) & (poi[:, 0] < w) & (poi[:, 1] >= 0) & (poi[:, 1] < h)

    if not valid_poi_mask.all():
        print(f"  Warning: Some poi coordinates are out of bounds! {valid_poi_mask.sum().item()}/{len(poi)} ({valid_poi_mask.float().mean()*100:.1f}%)")
        poi = poi[valid_poi_mask]
        points_3d = points_3d[valid_poi_mask]
        visible_lidar_idx = visible_lidar_idx[valid_poi_mask]
        visible_sem_mask = visible_sem_mask[valid_poi_mask]

    # 检查是否还有数据
    if poi.shape[0] == 0:
        return [], [], []

    mask_values = sam_mask[:, poi[:, 1], poi[:, 0]]  # shape: (n, P)

    obj_poi_coors = []
    obj_poi_idx = []
    obj_poi_labels = []

    for i in range(n):
        # 找到当前mask中值为1的点
        mask_indices = mask_values[i] == 1
        num_selected = mask_indices.sum().item()

        if num_selected == 0:
            # 如果没有选中的点，添加空张量
            filtered_coors = torch.empty((0, points_3d.shape[1]), dtype=points_3d.dtype, device=points_3d.device)
            filtered_idx = torch.empty((0,), dtype=visible_lidar_idx.dtype, device=visible_lidar_idx.device)
            filtered_labels = torch.empty((0,), dtype=visible_sem_mask.dtype, device=visible_sem_mask.device)
        else:
            # 提取对应的3D坐标、索引和语义标签 (直接使用已过滤的数据)
            try:
                filtered_coors = points_3d[mask_indices]  # (M, 3/4)
                filtered_idx = visible_lidar_idx[mask_indices]    # (M,)
                filtered_labels = visible_sem_mask[mask_indices]  # (M,)
            except RuntimeError as e:
                print(f"  Error in mask {i}: {e}")
                print(f"  mask_indices shape: {mask_indices.shape}")
                print(f"  mask_indices sum: {mask_indices.sum()}")
                print(f"  points_3d shape: {points_3d.shape}")
                raise e

        obj_poi_coors.append(filtered_coors)
        obj_poi_idx.append(filtered_idx)
        obj_poi_labels.append(filtered_labels)

    return obj_poi_coors, obj_poi_idx, obj_poi_labels

########################### hyperparameters ########################
# hyperparameters
BOX_TRESHOLD = 0.35
TEXT_TRESHOLD = 0.25
overlap_thre = 0.8
dino_nms_thresh = 0.3
# Mask overlap filtering parameters
ENABLE_MASK_OVERLAP_FILTER = True  # 是否启用mask重叠过滤
MASK_OVERLAP_THRESHOLD = 0.5  # mask重叠阈值，超过此值的重叠mask会被过滤
# query_len = 2000  # No longer needed without clustering
# No clustering strategy needed - each sample becomes one instance

sam_folder = 'gsam_uda'
pred_2d_inst_name = 'pred_inst_pts_xcluster'
USE_LOCAL_MASK = True
# USE_ALL_CLUSTERS = True  # No longer needed without clustering
SAVE_IMAGES = True
# KT, KL = 2000, 0 # select top kt and last kl clusters based on depth
VIEWS = ['CAM_FRONT', 'CAM_FRONT_RIGHT', 'CAM_FRONT_LEFT', 'CAM_BACK', 'CAM_BACK_LEFT', 'CAM_BACK_RIGHT']
all_texts = ['barrier', 'bicycle', 'bus', 'car', 'construction_vehicle', 'motorcycle', 'person', 'traffic-cone', 'trailer', 'truck', 'driveable_surface', 'other_flat', 'sidewalk', 'terrain', 'manmade', 'vegetation']
thing_texts = ['barrier', 'bicycle', 'bus', 'car', 'construction_vehicle', 'motorcycle', 'person', 'traffic-cone', 'trailer', 'truck']
text_labels = {'barrier': 1, 'bicycle': 2, 'bus': 3, 'car': 4, 'construction_vehicle': 5, 'motorcycle': 6, 'person': 7, 'traffic-cone': 8, 'trailer': 9, 'truck': 10, 'safty-cone': 8}
dule_texts = ["traffic-cone", "safty-cone"]
DEVICE = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

####################################################################

# system parameters
parser = argparse.ArgumentParser(description='Customized Grounded-Segment-Anything for 3DMM')
parser.add_argument('--data_mode', type=str, default='mini', help='trainval or mini')
parser.add_argument('--dset', type=str, default='train_1', help='train or val or train_1')
parser.add_argument('--part', type=str, default='1/1', help='split the dataset to parts')
# parser.add_argument('--server', type=str,  default='HPC3', help='which server to run the code')
dmode = parser.parse_args().data_mode
dset = parser.parse_args().dset
part = parser.parse_args().part



color_map = {
            0: [0, 0, 0],  # noise                 black
            1: [255, 120, 50],  # barrier               orange
            2: [255, 192, 203],  # bicycle               pink
            3: [255, 255, 0],  # bus                   yellow
            4: [0, 150, 245],  # car                   blue
            5: [0, 255, 255],  # construction_vehicle  cyan
            6: [255, 127, 0],  # motorcycle            dark orange
            7: [255, 0, 0],  # pedestrian            red
            8: [255, 240, 150],  # traffic-cone          light yellow
            9: [135, 60, 0],  # trailer               brown
            10: [160, 32, 240],  # truck                 purple
            11: [255, 0, 255],  # driveable_surface     dark pink
            12: [139, 137, 137],  # other_flat            dark red
            13: [75, 0, 75],  # sidewalk              dark purple
            14: [150, 240, 80],  # terrain               light green
            15: [230, 230, 250],  # manmade               white
            16: [0, 175, 0],  # vegetation            green
        }   

# load dataset list
proj_path = os.path.join(os.getcwd()+'/') 
if dmode=='trainval':
    data_root = 'data/nuscenes_full' 
else: 
    data_root = 'data/nuscenes_mini'
    
nusc = NuScenes(version=f'v1.0-{dmode}', dataroot=proj_path+data_root, verbose=True)
pklfile = os.path.join(proj_path, f'{data_root}/nuscenes_infos_{dset}.pkl')
with open(pklfile, 'rb') as f:
    data = pickle.load(f)
res_list = load_viewimages_path(data, data_root) # 'load_viewimages_path' will change the relative path to absolute path in 'data'
# res_list = load_point_mask_viewimages(data, data_root, False)

# load models
sys.path.append(os.path.join(os.getcwd(), "GroundingDINO"))
ckpt_repo_id = "ShilongLiu/GroundingDINO"
ckpt_filenmae = "groundingdino_swinb_cogcoor.pth"
ckpt_config_filename = "GroundingDINO_SwinB.cfg.py"
groundingdino_model = load_model_hf(ckpt_repo_id, ckpt_filenmae, ckpt_config_filename)

sam_checkpoint_path = 'checkpoints/sam/sam_vit_h_4b8939.pth'
sam_checkpoint = sam_checkpoint_path
sam = build_sam(checkpoint=sam_checkpoint)
sam.to(device=DEVICE)
sam_predictor = SamPredictor(sam)
data_prefix = {
        'dataset_path': data_root,
        'lidar_path': data_root+'/samples/LIDAR_TOP',
        'pts_panoptic_mask': data_root+'/panoptic/v1.0-mini',
        'CAM_FRONT': data_root+'/samples/CAM_FRONT',
        'CAM_FRONT_RIGHT': data_root+'/samples/CAM_FRONT_RIGHT',
        'CAM_FRONT_LEFT': data_root+'/samples/CAM_FRONT_LEFT',
        'CAM_BACK': data_root+'/samples/CAM_BACK',
        'CAM_BACK_LEFT': data_root+'/samples/CAM_BACK_LEFT',
        'CAM_BACK_RIGHT': data_root+'/samples/CAM_BACK_RIGHT',}

label_map = {
    1: 0, 5: 0, 7: 0, 8: 0, 10: 0, 11: 0, 13: 0, 19: 0, 20: 0, 0: 0, 29: 0, 31: 0,
    9: 1, 14: 2, 15: 3, 16: 3, 17: 4, 18: 5, 21: 6, 2: 7, 3: 7, 4: 7, 6: 7, 12: 8,
    22: 9, 23: 10, 24: 11, 25: 12, 26: 13, 27: 14, 28: 15, 30: 16

}


#### mkdir ###
folder_path = data['data_list'][0]['images']['CAM_FRONT']['img_path'].split('samples')[0]+sam_folder+'/'
if not os.path.exists(folder_path): 
    os.mkdir(folder_path) 
for p in ['sam_ground_seg', 'sam_ground_det', 'sam_ground_pth']:
    if not os.path.exists(folder_path+p):
        os.mkdir(folder_path+p)
    for v in VIEWS:
        path = folder_path+p+'/'+v
        if not os.path.exists(path):
            print(f'Make dir: {path}')
            os.mkdir(path) 


#### infer ####
tp, total, pred = 0, 0, 0
gt_p_b, pred_p_b, gt_p_s, pred_p_s = [], [], [], []
missed = []
if part == '1/1':
    start_idx = 0
    end_idx = len(data['data_list'])
else:
    ptarget = int(part.split('/')[0])-1
    ptotal = int(part.split('/')[1])
    num_total = len(data['data_list'])
    start_idx = int(ptarget)*(num_total//int(ptotal))
    end_idx = (int(ptarget)+1)*(num_total//int(ptotal)) 
    if int(ptarget) == int(ptotal)-1:
        end_idx = num_total
        print(f'Last part: {start_idx} to {end_idx}')
print('##############################################')
print(f'Processing from {start_idx} to {end_idx}.')
print('##############################################')
for i in tqdm(range(start_idx, end_idx)):
    # try:
    if True:
        # 1. load point cloud and semantic mask, initialize 
        save_path = res_list[i]['lidar_path'].replace('samples/LIDAR_TOP', f'{sam_folder}/{pred_2d_inst_name}').replace('.pcd.bin', '.h5') 
        
        print(f'$$$$$$$$$$$$$$$ Processing: {save_path} $$$$$$$$$$$$$$$')
        if not os.path.exists(os.path.dirname(save_path)):
            os.makedirs(os.path.dirname(save_path))
        # if os.path.exists(save_path):
        #     print(f'Skip file: {save_path} already exists.')    
        #     continue
        
        if 'lidar_path' in res_list[i].keys():
            lidar_point = load_point(res_list[i]['lidar_path'])
        else:
            lidar_path = res_list[i]['lidar_path']
            lidar_point = load_point(lidar_path)
        dataset_path = data_prefix.get('dataset_path')
        panoptic_path = data['data_list'][i]['pts_panoptic_mask_path']
        panoptic_path = os.path.join(dataset_path, panoptic_path)
        pts_semantic_mask, pts_inst_mask = load_mask(panoptic_path)
        pts_semantic_mask = np.vectorize(label_map.get)(pts_semantic_mask)
        sem_mask = pts_semantic_mask
        sample_token_v = data['data_list'][i]['token']
        # point_coor = lidar_point
        point_coor = np.concatenate((lidar_point, np.ones((lidar_point.shape[0], 1))), axis=1)

        N = lidar_point.shape[0]
        lidar_idx = torch.arange(0, N).to(DEVICE)# view = 'CAM_FRONT_LEFT'
        lidar_cluster = {}
        lidar_cluster_labels = {}
        lidar_cluster_class_scores = {}
        lidar_cluster_mask_scores = {}
        lidar_cluster_classes = {}
        cluster_num = 0
        
        for v in VIEWS: 
            save_seg_path = os.path.join(proj_path, data['data_list'][i]['images'][v]['img_path'].replace('samples', sam_folder+'/sam_ground_seg'))
            save_det_path = os.path.join(proj_path, data['data_list'][i]['images'][v]['img_path'].replace('samples', sam_folder+'/sam_ground_det'))
            save_pth_path = os.path.join(proj_path, data['data_list'][i]['images'][v]['img_path'].replace('samples', sam_folder+'/sam_ground_pth').replace('.jpg', '.pth'))
            
            sample_v = nusc.get('sample', sample_token_v)
            pointsensor_token_v = nusc.get('sample_data', sample_v['data']['LIDAR_TOP'])
            point_calibrated_sensor_token_v, point_ego_pose_token_v = pointsensor_token_v['calibrated_sensor_token'], pointsensor_token_v['ego_pose_token']
            cam_token_v = sample_v['data'][v]
            point_cam_coords1, coloring_p, im_p, mask_p = map_pointcloud_to_image_2(
                nusc, point_coor, 
                point_calibrated_sensor_token_v, point_ego_pose_token_v, 
                cam_token_v, min_dist=1.0, return_img=True,
                sem_mask=sem_mask,
                use_label_map=False)
            
            # # visualize poi
            # img_rendered_poi = plot_point_in_camview_ret(point_cam_coords1[:, mask_p], coloring_p[mask_p, :], im_p, dot_size=4)

            coor = torch.tensor(point_coor[mask_p]).to(DEVICE)
            poi = point_cam_coords1[:, mask_p][:2, :].T.astype(int) # (N, 2)
            poi = torch.tensor(poi).to(DEVICE)
            poi = poi[:,[1,0]]
            
            img_path = res_list[i]['imgs_meta']['img_path'][v]
            image_source, image = load_image(img_path)
            H, W, _ = image_source.shape
            target_bboxes, obj_labels, all_cls_scores = [], [], []
            annotated_frame = image_source.copy()
            
            
            # print('Start inferencing...')
            # project point cloud to camera view
            print('Processing:', save_pth_path)
            if os.path.exists(save_pth_path) and USE_LOCAL_MASK:
                # print(f'Skip file: {save_pth_path} already exists.')
                sam_res = torch.load(save_pth_path)
                sam_masks = sam_res['masks']
                sam_masks = torch.from_numpy(sam_masks).to(DEVICE) if isinstance(sam_masks, np.ndarray) else sam_masks.to(DEVICE)
                sam_masks = expand_mask_torch(sam_masks)
                det_scores = sam_res['class_scores']
                mask_scores = sam_res['mask_scores']
                sam_classes = sam_res['obj_labels']
                assert len(torch.unique(sam_masks))-1 <= det_scores.shape[0], "The number of masks and scores should be the same." # may filter out some masks by nms
                
            else:
                # # write the error to a file
                # part_ = part.replace('/','_')
                # with open(f'error_{dmode}_{dset}_{part_}.txt', 'a') as f:
                #     f.write(f'File not found: {save_pth_path} belongs to lidar point {i}, {save_path}.\n')
                # continue
                # assert os.path.exists(save_pth_path)
                
                ################# inference #################
                for d, TEXT_PROMPT in enumerate(thing_texts):                
                    
                    # step-1: detection
                    # 1. vanailla DINO
                    if TEXT_PROMPT != "traffic-cone":
                        boxes, logits, phrases = predict(
                            model=groundingdino_model, 
                            image=image, 
                            caption=TEXT_PROMPT, 
                            box_threshold=BOX_TRESHOLD, 
                            text_threshold=TEXT_TRESHOLD,
                            device=DEVICE
                        )
                        if boxes.shape[0] == 0:
                            # print(f"Frame {i}, View {view}: No object detected.")
                            continue

                    else:
                        # 2. dule check with two prompts
                        boxes_cand, logits_cand, phrases_cand = {}, {}, {}
                        for TEXT_PROMPT in dule_texts:
                            boxes, logits, phrases = predict(
                                model=groundingdino_model, 
                                image=image, 
                                caption=TEXT_PROMPT, 
                                box_threshold=BOX_TRESHOLD, 
                                text_threshold=TEXT_TRESHOLD,
                                device=DEVICE
                            )
                            boxes_cand[TEXT_PROMPT] = boxes
                            logits_cand[TEXT_PROMPT] = logits
                            phrases_cand[TEXT_PROMPT] = phrases
                            print(f"Frame {i}, View {v}: {boxes.shape[0]} boxes detected for {TEXT_PROMPT}. phrases: {phrases}")
                    
                        # # select the overlapped boxes
                        # boxes, logits, phrases = keep_overlaped(boxes_cand[dule_texts[0]], boxes_cand[dule_texts[1]],
                        #                         logits_cand[dule_texts[0]], logits_cand[dule_texts[1]],
                        #                         phrases_cand[dule_texts[0]], phrases_cand[dule_texts[1]],
                        #                         overlap_thre)
                        # save all boxes
                        boxes = torch.cat([boxes_cand[dule_texts[0]], boxes_cand[dule_texts[1]]], dim=0)
                        logits = torch.cat([logits_cand[dule_texts[0]], logits_cand[dule_texts[1]]], dim=0)
                        phrases = [phrases_cand[dule_texts[0]] for _ in range(boxes_cand[dule_texts[0]].shape[0])] + [phrases_cand[dule_texts[1]] for _ in range(boxes_cand[dule_texts[1]].shape[0])]
                        print(f"@@@@@@@@@@@@@@@@@@@@ Frame {i}, View {v}: {boxes.shape[0]} boxes detected.")
                        print(phrases)
                        if boxes.shape[0] == 0:
                            continue

                    # annotated_frame = annotate(image_source=image_source, boxes=boxes, logits=logits, phrases=phrases)
                    # box: normalized box xywh -> unnormalized xyxy
                    H, W, _ = image_source.shape
                    boxes_xyxy = box_ops.box_cxcywh_to_xyxy(boxes) * torch.Tensor([W, H, W, H])
                    
                    # step-1.5: filter with nms
                    # filter with nms
                    # if '' in phrases: #TODO: not know why
                    #     phrases = [thing_texts[d] for _ in phrases]

                    boxes_xyxy, cls_scores, phrases, keep = filter_boxes(boxes_xyxy, logits, phrases, thing_texts=thing_texts, thre=dino_nms_thresh)
                    boxes = boxes[keep]
                    assert len(boxes) == len(cls_scores) == len(phrases)

                    if len(boxes) == 0:
                        print(f"Frame {i}, View {v}: No object detected.")
                        continue
                    
                    if SAVE_IMAGES:
                        # # print each instance bbox, score and class
                        # for box, score, phrase in zip(boxes_xyxy, cls_scores, phrases):
                        #     print(f"Box: {box}, Score: {score}, Phrase: {phrase}")
                        annotated_frame = annotate(image_source=annotated_frame, boxes=boxes, logits=cls_scores, phrases=phrases)
                        annotated_frame = annotated_frame[...,::-1] # BGR to RGB
                    # boxes_xyxy = box_ops.box_cxcywh_to_xyxy(boxes) * torch.Tensor([W, H, W, H])
                    target_bboxes.append(boxes_xyxy)
                    all_cls_scores.append(cls_scores)
                    for _ in range(boxes_xyxy.shape[0]):
                        obj_labels.append(TEXT_PROMPT)
                   
                if SAVE_IMAGES: 
                    plt.figure(figsize=(16, 9))
                    plt.imshow(annotated_frame)
                    plt.axis('off')
                    plt.savefig(save_det_path)
                    # print(f'Save to {save_det_path}')
                    plt.close()

                    if target_bboxes != []:
                        target_bboxes = torch.cat(target_bboxes, dim=0)
                        all_cls_scores = torch.cat(all_cls_scores, dim=0)
                    else:
                        # save the empty results
                        res = np.array(image_source)
                        plt.figure(figsize=(16, 9))
                        plt.imshow(res)
                        plt.axis('off')
                        plt.savefig(save_seg_path)
                        # print(f'Save to {save_seg_path}')
                        plt.close()
                        continue
                else:
                    if target_bboxes != []:
                        target_bboxes = torch.cat(target_bboxes, dim=0)
                        all_cls_scores = torch.cat(all_cls_scores, dim=0)

                # set image
                sam_predictor.set_image(image_source)
                transformed_boxes = sam_predictor.transform.apply_boxes_torch(target_bboxes, image_source.shape[:2]).to(DEVICE)
                masks, mask_scores, mask_logits = sam_predictor.predict_torch(
                            point_coords = None,
                            point_labels = None,
                            boxes = transformed_boxes,
                            multimask_output = False,
                        )

                masks_ = masks.squeeze(1)
                if SAVE_IMAGES:
                    res = plot_anns(pack_anno(masks_, mask_scores, target_bboxes, obj_labels), image_source) # show all masks on the image, w/o bbox, w/ poi
                    plt.figure(figsize=(16, 9))
                    plt.imshow(res)
                    plt.axis('off')
                    plt.savefig(save_seg_path)
                    plt.close()
                
                masks_ = masks_.cpu()
                det_scores = all_cls_scores.cpu()  # DINO的classification scores
                mask_scores = mask_scores.cpu()  # SAM的mask quality scores
                mask_logits = mask_logits.cpu()  # SAM的mask logits
                target_bboxes = target_bboxes.cpu()

                # 确保数量一致性
                assert masks_.shape[0] == det_scores.shape[0] == mask_scores.shape[0], \
                    f"Shape mismatch: masks {masks_.shape[0]}, class_scores {det_scores.shape}, mask_scores {mask_scores.shape[0]}"

                # 可选的mask重叠过滤
                if ENABLE_MASK_OVERLAP_FILTER and masks_.shape[0] > 1:
                    filtered_masks, filtered_det_scores, filtered_obj_labels, keep_indices = filter_overlapping_masks(
                        masks_, det_scores, obj_labels, overlap_threshold=MASK_OVERLAP_THRESHOLD
                    )

                    filtered_mask_scores = mask_scores[keep_indices]
                    filtered_mask_logits = mask_logits[keep_indices]
                    filtered_target_bboxes = target_bboxes[keep_indices]

                    masks_ = filtered_masks
                    det_scores = filtered_det_scores
                    mask_scores = filtered_mask_scores
                    mask_logits = filtered_mask_logits
                    target_bboxes = filtered_target_bboxes
                    obj_labels = filtered_obj_labels
                
                debug_save_path = save_pth_path.replace('.pth', '_mask_debug.png')

                torch.save({'masks': compress_mask(masks_, scale_factor=0.4), # resize the mask to save space
                            'class_scores': det_scores,  # DINO classification scores (n,)
                            'mask_scores': mask_scores,  # SAM mask quality scores (n, 1)
                            'mask_logits': mask_logits,  # SAM mask logits (n, 1, 256, 256)
                            'target_bboxes': target_bboxes,
                            'obj_labels': obj_labels},
                            save_pth_path)
                sam_masks = masks_.squeeze(1).to(DEVICE)
                sam_classes = obj_labels

            ######### project to 3D using optimized method #########
            # Use the optimized function to filter 3D points in masks
            obj_poi_coors, obj_poi_idx, obj_poi_labels = filter_3d_points_in_mask_optimized(
                coor, poi, sam_masks, mask_p, lidar_idx, sem_mask
            )
            
            # print('*********:', len(obj_poi_coors), det_scores.shape, len(sam_classes))
            for k in range(len(obj_poi_coors)):
                sample_coors = obj_poi_coors[k]  # (M, 3) tensor
                sample_3d_idx = obj_poi_idx[k]   # (M,) tensor
                sample_label = obj_poi_labels[k] # (M,) tensor
                class_score = det_scores[k]
                mask_score = mask_scores[k]
                pred_cls = sam_classes[k]

                ################## directly use sample as instance mask ##################
                if sample_coors.shape[0] == 0:
                    continue

                # Each sample corresponds to one 3D predicted instance mask
                lidar_cluster[cluster_num] = sample_3d_idx
                lidar_cluster_labels[cluster_num] = sample_label
                lidar_cluster_class_scores[cluster_num] = class_score
                lidar_cluster_mask_scores[cluster_num] = mask_score
                lidar_cluster_classes[cluster_num] = pred_cls
                cluster_num += 1
                #-----------------------------------------------------------------------#
                    
        # pack cluster_id and semantic label to point cloud
        pts_inst_cluster = torch.zeros((N)).to(DEVICE)
        pts_inst_cluster_color = torch.zeros((N,3)).to(DEVICE)
        pts_inst_cluster_class_score = torch.zeros((N)).to(DEVICE)
        pts_inst_cluster_mask_score = torch.zeros((N)).to(DEVICE)
        pts_inst_cluster_classes = torch.zeros((N)).to(DEVICE)
        for n in lidar_cluster.keys():
            semantic_label = lidar_cluster_labels[n]
            pts_inst_cluster[lidar_cluster[n].squeeze()] = n
            pts_inst_cluster_class_score[lidar_cluster[n].squeeze()] = lidar_cluster_class_scores[n]
            pts_inst_cluster_mask_score[lidar_cluster[n].squeeze()] = lidar_cluster_mask_scores[n]
            pts_inst_cluster_classes[lidar_cluster[n].squeeze()] = text_labels[lidar_cluster_classes[n]]
            color = torch.zeros((semantic_label.shape[0], 3)).to(DEVICE)
            for idx, p in enumerate(semantic_label):
                color[idx] = torch.tensor(color_map[int(p)])/255
            pts_inst_cluster_color[lidar_cluster[n].squeeze()] = color
        ########################################################################
             
        # save the cluster results
        with h5py.File(save_path, 'w') as f:
            f.create_dataset('mask', data=pts_inst_cluster.cpu().numpy().astype(np.uint16), compression='gzip')
            f.create_dataset('class_score', data=pts_inst_cluster_class_score.cpu().numpy().astype(np.float32), compression='gzip')
            f.create_dataset('mask_score', data=pts_inst_cluster_mask_score.cpu().numpy().astype(np.float32), compression='gzip')
            f.create_dataset('classes', data=pts_inst_cluster_classes.cpu().numpy().astype(np.uint8), compression='gzip')

        ############# CAL P&R ###############
        ## load gt
        if 'pts_instance_mask' in res_list[i].keys():
            pts_instance_mask = res_list[i]['pts_instance_mask']
            pts_semantic_mask = res_list[i]['pts_semantic_mask']
        else:
            pts_semantic_mask, pts_instance_mask = load_mask(panoptic_path)
            pts_semantic_mask = np.vectorize(label_map.get)(pts_semantic_mask) # map to 0-19 classes
        
        gt_pts_inst_thing = pts_instance_mask.copy()
        gt_pts_inst_thing[(pts_semantic_mask==0)|(pts_semantic_mask>10)] = 0
        gt_pts_inst_thing = torch.from_numpy(gt_pts_inst_thing).to(DEVICE)
        
        ## load pred
        pred_pts_inst = torch.from_numpy(pts_inst_cluster).to(DEVICE) if isinstance(pts_inst_cluster, np.ndarray) else pts_inst_cluster.to(DEVICE)
        print(torch.unique(pred_pts_inst))
        recall = cand_recall_calc(gt_pts_inst_thing, pred_pts_inst)
        tp += recall.sum().float()
        pred += len(torch.unique(pred_pts_inst))-1
        total += recall.shape[0]

print('$'*20)
print('precision dscore:', tp/pred, tp.cpu().numpy(), '/', pred)
print('recall dscore:', tp/total, tp.cpu().numpy(), '/', total)
