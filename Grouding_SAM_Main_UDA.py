import os
import argparse
import sys
import copy
import numpy as np
import torch
import argparse
from PIL import Image, ImageDraw, ImageFont
from torchvision.ops import box_convert
import pickle
from tqdm import tqdm
from nuscenes.nuscenes import NuScenes
import h5py
import json
import numpy as np
from PIL import Image
import os
from skimage.transform import resize

# Grounding DINO
sys.path.append('tool-dino/Grounded-Segment-Anything')
import GroundingDINO.groundingdino.datasets.transforms as T
from GroundingDINO.groundingdino.models import build_model
from GroundingDINO.groundingdino.util import box_ops
from GroundingDINO.groundingdino.util.slconfig import SLConfig
from GroundingDINO.groundingdino.util.utils import clean_state_dict, get_phrases_from_posmap
from GroundingDINO.groundingdino.util.inference import annotate, load_image, predict
from huggingface_hub import hf_hub_download

# segment anything
try:    
    from segment_anything.segment_anything import build_sam, SamPredictor 
except:
    from segment_anything import build_sam, SamPredictor 
import cv2
import numpy as np
import matplotlib.pyplot as plt
from Grouding_SAM_Utils import load_image_from_np, load_model_hf, plot_anns, pack_anno, keep_overlaped, filter_boxes, plot_point_in_camview

from sklearn.cluster import DBSCAN
from sklearn.metrics import fowlkes_mallows_score
from sklearn.metrics import homogeneity_score, completeness_score, v_measure_score

# load pkl
sys.path.append('tools')
from pkl_utils import load_viewimages_path, load_point_mask_viewimages
from general_tool import load_point, load_mask, load_viewimages, compress_mask, expand_mask, expand_mask_torch
from projection.pc2img import map_pointcloud_to_image_2 
from cluster_utils import get_comfusion_matrix
from heatmap_convertor import cand_recall_calc

def save_masks_and_annotations_coco_format(masks, class_scores, mask_scores, bboxes, obj_labels,
                                          save_path, thing_texts, scale_factor=0.4):
    """
    保存mask为图像格式，保存scores和labels为COCO JSON格式

    Args:
        masks: torch.Tensor, shape (N, H, W) - 二值mask
        class_scores: torch.Tensor, shape (N,) - DINO分类分数
        mask_scores: torch.Tensor, shape (N, 1) - SAM mask质量分数
        bboxes: torch.Tensor, shape (N, 4) - 边界框
        obj_labels: list - 类别标签字符串列表
        save_path: str - 保存路径（.pth文件路径）
        thing_texts: list - 类别名称列表
        scale_factor: float - 缩放因子
    """

    # 获取保存目录和文件名
    save_dir = os.path.dirname(save_path)
    base_name = os.path.splitext(os.path.basename(save_path))[0]

    # 确保保存目录存在
    os.makedirs(save_dir, exist_ok=True)
    os.makedirs(os.path.join(save_dir, "mask"), exist_ok=True)
    os.makedirs(os.path.join(save_dir, "annotation"), exist_ok=True)

    # 转换为numpy并调整尺寸
    masks_np = masks.cpu().numpy()
    n, h, w = masks_np.shape

    # 调整mask尺寸
    new_h, new_w = int(h * scale_factor), int(w * scale_factor)

    # 创建instance mask图像（每个像素值表示instance ID）
    instance_mask = np.zeros((new_h, new_w), dtype=np.uint16)

    # 准备COCO格式的annotations
    annotations = []

    for i in range(n):
        # 调整mask尺寸
        resized_mask = resize(masks_np[i], (new_h, new_w),
                            order=0, preserve_range=True, anti_aliasing=False).astype(bool)

        # 将当前mask添加到instance mask中（instance ID从1开始）
        instance_mask[resized_mask] = i + 1

        # 获取类别ID
        try:
            category_id = thing_texts.index(obj_labels[i])
        except ValueError:
            category_id = len(thing_texts)  # 如果找不到，使用默认ID

        # 创建annotation
        annotation = {
            "id": i + 1,  # instance ID（从1开始）
            "category_id": category_id,
            "class_name": obj_labels[i],
            "class_score": float(class_scores[i].cpu()),
            "mask_score": float(mask_scores[i].cpu().squeeze()),
            "bbox": [float(x) for x in bboxes[i].cpu().tolist()],  # [x1, y1, x2, y2]
            "area": int(resized_mask.sum())
        }
        annotations.append(annotation)

    # 保存instance mask为图像
    mask_save_path = os.path.join(save_dir, "mask", f"{base_name}.png")
    Image.fromarray(instance_mask).save(mask_save_path)

    # 保存annotations为JSON
    json_save_path = os.path.join(save_dir, "annotation", f"{base_name}.json")

    # 创建完整的COCO格式数据
    coco_data = {
        "info": {
            "description": "3D Multi-Modal Panoptic Segmentation Results",
            "version": "1.0",
            "year": 2024
        },
        "images": [
            {
                "id": 1,
                "width": new_w,
                "height": new_h,
                "file_name": f"{base_name}_masks.png"
            }
        ],
        "categories": [
            {"id": i, "name": name} for i, name in enumerate(thing_texts)
        ],
        "annotations": annotations
    }

    with open(json_save_path, 'w') as f:
        json.dump(coco_data, f, indent=2)

    # # 同时保存原始格式以保持兼容性（可选）
    # if False:  # 设置为True如果需要保持兼容性
    #     torch.save({'masks': compress_mask(masks, scale_factor=scale_factor),
    #                 'class_scores': class_scores,
    #                 'mask_scores': mask_scores,
    #                 'mask_logits': None,  # 不保存logits以节省空间
    #                 'target_bboxes': bboxes,
    #                 'obj_labels': obj_labels},
    #                save_path)

def load_masks_and_annotations_coco_format(save_path):
    """
    加载COCO格式保存的mask和annotations数据

    Args:
        save_path: str - 原始保存路径（.pth文件路径）

    Returns:
        tuple: (masks, class_scores, mask_scores, bboxes, obj_labels, coco_data)
            - masks: torch.Tensor, shape (N, H, W) - 重构的二值mask
            - class_scores: torch.Tensor, shape (N,) - DINO分类分数
            - mask_scores: torch.Tensor, shape (N, 1) - SAM mask质量分数
            - bboxes: torch.Tensor, shape (N, 4) - 边界框
            - obj_labels: list - 类别标签字符串列表
            - coco_data: dict - 完整的COCO格式数据
    """
    import json
    import numpy as np
    from PIL import Image
    import os
    import torch

    # 获取对应的文件路径
    save_dir = os.path.dirname(save_path)
    base_name = os.path.splitext(os.path.basename(save_path))[0]

    mask_path = os.path.join(save_dir, f"{base_name}_masks.png")
    json_path = os.path.join(save_dir, f"{base_name}_annotations.json")

    # 检查文件是否存在
    if not os.path.exists(mask_path):
        raise FileNotFoundError(f"Mask file not found: {mask_path}")
    if not os.path.exists(json_path):
        raise FileNotFoundError(f"JSON file not found: {json_path}")

    # 加载instance mask图像
    mask_img = Image.open(mask_path)
    instance_mask = np.array(mask_img)

    # 加载JSON数据
    with open(json_path, 'r') as f:
        coco_data = json.load(f)

    annotations = coco_data['annotations']
    n_instances = len(annotations)

    if n_instances == 0:
        # 返回空数据
        h, w = instance_mask.shape
        return (torch.zeros((0, h, w), dtype=torch.bool),
                torch.zeros((0,)),
                torch.zeros((0, 1)),
                torch.zeros((0, 4)),
                [],
                coco_data)

    # 获取图像尺寸
    h, w = instance_mask.shape

    # 重构individual masks
    masks = torch.zeros((n_instances, h, w), dtype=torch.bool)
    class_scores = torch.zeros(n_instances)
    mask_scores = torch.zeros((n_instances, 1))
    bboxes = torch.zeros((n_instances, 4))
    obj_labels = []

    for i, ann in enumerate(annotations):
        instance_id = ann['id']

        # 提取当前instance的mask
        mask = (instance_mask == instance_id)
        masks[i] = torch.from_numpy(mask)

        # 提取scores和其他信息
        class_scores[i] = ann['class_score']
        mask_scores[i, 0] = ann['mask_score']
        bboxes[i] = torch.tensor(ann['bbox'])
        obj_labels.append(ann['class_name'])

    print(f"Loaded {n_instances} instances from:")
    print(f"  - Mask image: {mask_path}")
    print(f"  - Annotations: {json_path}")

    return masks, class_scores, mask_scores, bboxes, obj_labels, coco_data

def load_masks_and_annotations_coco_format_by_files(mask_path, json_path):
    """
    直接通过文件路径加载COCO格式的mask和annotations数据

    Args:
        mask_path: str - mask图像文件路径
        json_path: str - JSON标注文件路径

    Returns:
        tuple: (masks, class_scores, mask_scores, bboxes, obj_labels, coco_data)
    """
    import json
    import numpy as np
    from PIL import Image
    import torch

    # 检查文件是否存在
    if not os.path.exists(mask_path):
        raise FileNotFoundError(f"Mask file not found: {mask_path}")
    if not os.path.exists(json_path):
        raise FileNotFoundError(f"JSON file not found: {json_path}")

    # 加载instance mask图像
    mask_img = Image.open(mask_path)
    instance_mask = np.array(mask_img)

    # 加载JSON数据
    with open(json_path, 'r') as f:
        coco_data = json.load(f)

    annotations = coco_data['annotations']
    n_instances = len(annotations)

    if n_instances == 0:
        # 返回空数据
        h, w = instance_mask.shape
        return (torch.zeros((0, h, w), dtype=torch.bool),
                torch.zeros((0,)),
                torch.zeros((0, 1)),
                torch.zeros((0, 4)),
                [],
                coco_data)

    # 获取图像尺寸
    h, w = instance_mask.shape

    # 重构individual masks
    masks = torch.zeros((n_instances, h, w), dtype=torch.bool)
    class_scores = torch.zeros(n_instances)
    mask_scores = torch.zeros((n_instances, 1))
    bboxes = torch.zeros((n_instances, 4))
    obj_labels = []

    for i, ann in enumerate(annotations):
        instance_id = ann['id']

        # 提取当前instance的mask
        mask = (instance_mask == instance_id)
        masks[i] = torch.from_numpy(mask)

        # 提取scores和其他信息
        class_scores[i] = ann['class_score']
        mask_scores[i, 0] = ann['mask_score']
        bboxes[i] = torch.tensor(ann['bbox'])
        obj_labels.append(ann['class_name'])

    print(f"Loaded {n_instances} instances from:")
    print(f"  - Mask image: {mask_path}")
    print(f"  - Annotations: {json_path}")

    return masks, class_scores, mask_scores, bboxes, obj_labels, coco_data

def save_dual_subplot_image(left_image, right_image, left_title, right_title, save_path):
    """
    保存包含两个subplot的图像

    Args:
        left_image: 左侧图像
        right_image: 右侧图像
        left_title: 左侧图像标题
        right_title: 右侧图像标题
        save_path: 保存路径
    """
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(32, 9))

    # 左图
    ax1.imshow(left_image)
    ax1.set_title(left_title, fontsize=14)
    ax1.axis('off')

    # 右图
    ax2.imshow(right_image)
    ax2.set_title(right_title, fontsize=14)
    ax2.axis('off')

    plt.tight_layout()
    plt.savefig(save_path, dpi=150, bbox_inches='tight')
    plt.close()

def filter_overlapping_masks(masks, class_scores, obj_labels, overlap_threshold=0.5):
    """
    过滤重叠的masks，当overlap大于阈值时保留class_score更高的mask

    Args:
        masks (torch.Tensor): 形状为 (N, H, W) 的二值mask
        class_scores (torch.Tensor): 形状为 (N,) 的分类分数
        obj_labels (list): 长度为 N 的类别标签列表
        overlap_threshold (float): overlap阈值，默认0.5

    Returns:
        tuple: (filtered_masks, filtered_scores, filtered_labels, keep_indices)
    """
    import torch

    n = masks.shape[0]
    if n <= 1:
        return masks, class_scores, obj_labels, torch.arange(n)


    # 计算所有mask pairs的overlap
    masks_flat = masks.view(n, -1).float()  # (N, H*W)

    # 计算intersection和union
    intersections = torch.mm(masks_flat, masks_flat.t())  # (N, N)
    areas = masks_flat.sum(dim=1)  # (N,)
    unions = areas.unsqueeze(0) + areas.unsqueeze(1) - intersections  # (N, N)

    # 计算IoU矩阵，避免除零
    ious = intersections / (unions + 1e-8)

    # 只考虑上三角矩阵（避免重复计算）
    triu_mask = torch.triu(torch.ones_like(ious, dtype=torch.bool), diagonal=1)
    overlapping_pairs = torch.where((ious > overlap_threshold) & triu_mask)


    # 记录需要移除的mask索引
    to_remove = set()

    for i, j in zip(overlapping_pairs[0], overlapping_pairs[1]):
        i, j = i.item(), j.item()

        # 如果两个mask都还没被标记为移除
        if i not in to_remove and j not in to_remove:
            overlap_ratio = ious[i, j].item()
            score_i = class_scores[i].item()
            score_j = class_scores[j].item()
            # 保留分数更高的mask
            if score_i >= score_j:
                to_remove.add(j)
            else:
                to_remove.add(i)

    # 创建保留的索引
    keep_indices = torch.tensor([i for i in range(n) if i not in to_remove], dtype=torch.long)


    if len(keep_indices) == 0:
        # 如果所有mask都被移除，至少保留分数最高的一个
        best_idx = torch.argmax(class_scores)
        keep_indices = torch.tensor([best_idx])

    # 过滤结果
    filtered_masks = masks[keep_indices]
    filtered_scores = class_scores[keep_indices]
    filtered_labels = [obj_labels[i] for i in keep_indices]

    return filtered_masks, filtered_scores, filtered_labels, keep_indices

def get_detailed_classification_scores(model, image, caption, box_threshold, text_threshold, device="cuda"):
    """
    获取详细的classification scores，包括每个token的分数

    Returns:
        boxes: 检测框
        max_scores: 每个框的最大分数
        all_scores: 每个框对所有token的分数 (n, 256)
        phrases: 检测到的短语
    """
    from GroundingDINO.groundingdino.util.inference import preprocess_caption
    from GroundingDINO.groundingdino.util.utils import get_phrases_from_posmap

    caption = preprocess_caption(caption=caption)
    model = model.to(device)
    image = image.to(device)

    with torch.no_grad():
        outputs = model(image[None], captions=[caption])

    prediction_logits = outputs["pred_logits"].cpu().sigmoid()[0]  # (nq, 256)
    prediction_boxes = outputs["pred_boxes"].cpu()[0]  # (nq, 4)

    mask = prediction_logits.max(dim=1)[0] > box_threshold
    all_scores = prediction_logits[mask]  # (n, 256) - 所有token的分数
    boxes = prediction_boxes[mask]  # (n, 4)
    max_scores = all_scores.max(dim=1)[0]  # (n,) - 每个框的最大分数

    tokenizer = model.tokenizer
    tokenized = tokenizer(caption)

    phrases = [
        get_phrases_from_posmap(logit > text_threshold, tokenized, tokenizer).replace('.', '')
        for logit in all_scores
    ]

    return boxes, max_scores, all_scores, phrases

def filter_3d_points_in_mask_optimized(points_3d, poi, sam_mask, view_mask, lidar_idx, sem_mask):
    """优化版本：使用向量化操作"""
    poi = poi.round().to(torch.int64)
    n, h, w = sam_mask.shape
    N = points_3d.shape[0]
    sel_idx = torch.arange(0, N, device=poi.device)[view_mask]
    assert (poi[:, 0] >= 0).all() and (poi[:, 0] < h).all() and (poi[:, 1] >= 0).all() and (poi[:, 1] < w).all()
    
    visible_lidar_idx = lidar_idx[view_mask].to(torch.int64)
    visible_sem_mask = torch.tensor(sem_mask[view_mask]).to(poi.device).to(torch.int64)
    mask_values = sam_mask[:, poi[:, 0], poi[:, 1]]
    
    obj_coors = []
    obj_idxs = []
    obj_labels = []
    for i in range(n):
        mask_indices = mask_values[i] == 1
        obj_coors.append(poi[mask_indices])
        obj_idxs.append(sel_idx[mask_indices])
        obj_labels.append(visible_sem_mask[mask_indices])
        
    return obj_coors, obj_idxs, obj_labels


########################### hyperparameters ########################
# hyperparameters
BOX_TRESHOLD = 0.35
TEXT_TRESHOLD = 0.25
overlap_thre = 0.8
dino_nms_thresh = 0.3
ENABLE_MASK_OVERLAP_FILTER = True  
MASK_OVERLAP_THRESHOLD = 0.8  

sam_folder = 'gsam_uda'
pred_2d_inst_name = 'pred_inst_pts_xcluster'
USE_LOCAL_MASK = True
SAVE_IMAGES = True
VIEWS = ['CAM_FRONT', 'CAM_FRONT_RIGHT', 'CAM_FRONT_LEFT', 'CAM_BACK', 'CAM_BACK_LEFT', 'CAM_BACK_RIGHT']
all_texts = ['barrier', 'bicycle', 'bus', 'car', 'construction_vehicle', 'motorcycle', 'person', 'traffic-cone', 'trailer', 'truck', 'driveable_surface', 'other_flat', 'sidewalk', 'terrain', 'manmade', 'vegetation']
thing_texts = ['barrier', 'bicycle', 'bus', 'car', 'construction_vehicle', 'motorcycle', 'person', 'traffic-cone', 'trailer', 'truck']
text_labels = {'barrier': 1, 'bicycle': 2, 'bus': 3, 'car': 4, 'construction_vehicle': 5, 'motorcycle': 6, 'person': 7, 'traffic-cone': 8, 'trailer': 9, 'truck': 10, 'safty-cone': 8}
dule_texts = ["traffic-cone", "safty-cone"]
DEVICE = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

####################################################################

# system parameters
parser = argparse.ArgumentParser(description='Customized Grounded-Segment-Anything for 3DMM')
parser.add_argument('--data_mode', type=str, default='mini', help='trainval or mini')
parser.add_argument('--dset', type=str, default='train_1', help='train or val or train_1')
parser.add_argument('--part', type=str, default='1/1', help='split the dataset to parts')
dmode = parser.parse_args().data_mode
dset = parser.parse_args().dset
part = parser.parse_args().part



color_map = {
            0: [0, 0, 0],  # noise                 black
            1: [255, 120, 50],  # barrier               orange
            2: [255, 192, 203],  # bicycle               pink
            3: [255, 255, 0],  # bus                   yellow
            4: [0, 150, 245],  # car                   blue
            5: [0, 255, 255],  # construction_vehicle  cyan
            6: [255, 127, 0],  # motorcycle            dark orange
            7: [255, 0, 0],  # pedestrian            red
            8: [255, 240, 150],  # traffic-cone          light yellow
            9: [135, 60, 0],  # trailer               brown
            10: [160, 32, 240],  # truck                 purple
            11: [255, 0, 255],  # driveable_surface     dark pink
            12: [139, 137, 137],  # other_flat            dark red
            13: [75, 0, 75],  # sidewalk              dark purple
            14: [150, 240, 80],  # terrain               light green
            15: [230, 230, 250],  # manmade               white
            16: [0, 175, 0],  # vegetation            green
        }   

# load dataset list
proj_path = os.path.join(os.getcwd()+'/') 
if dmode=='trainval':
    data_root = 'data/nuscenes_full' 
else: 
    data_root = 'data/nuscenes_mini'
    
nusc = NuScenes(version=f'v1.0-{dmode}', dataroot=proj_path+data_root, verbose=True)
pklfile = os.path.join(proj_path, f'{data_root}/nuscenes_infos_{dset}.pkl')
with open(pklfile, 'rb') as f:
    data = pickle.load(f)
res_list = load_viewimages_path(data, data_root) # 'load_viewimages_path' will change the relative path to absolute path in 'data'

# load models
sys.path.append(os.path.join(os.getcwd(), "GroundingDINO"))
ckpt_repo_id = "ShilongLiu/GroundingDINO"
ckpt_filenmae = "groundingdino_swinb_cogcoor.pth"
ckpt_config_filename = "GroundingDINO_SwinB.cfg.py"
groundingdino_model = load_model_hf(ckpt_repo_id, ckpt_filenmae, ckpt_config_filename)

sam_checkpoint_path = 'checkpoints/sam/sam_vit_h_4b8939.pth'
sam_checkpoint = sam_checkpoint_path
sam = build_sam(checkpoint=sam_checkpoint)
sam.to(device=DEVICE)
sam_predictor = SamPredictor(sam)
data_prefix = {
        'dataset_path': data_root,
        'lidar_path': data_root+'/samples/LIDAR_TOP',
        'pts_panoptic_mask': data_root+'/panoptic/v1.0-mini',
        'CAM_FRONT': data_root+'/samples/CAM_FRONT',
        'CAM_FRONT_RIGHT': data_root+'/samples/CAM_FRONT_RIGHT',
        'CAM_FRONT_LEFT': data_root+'/samples/CAM_FRONT_LEFT',
        'CAM_BACK': data_root+'/samples/CAM_BACK',
        'CAM_BACK_LEFT': data_root+'/samples/CAM_BACK_LEFT',
        'CAM_BACK_RIGHT': data_root+'/samples/CAM_BACK_RIGHT',}

label_map = {
    1: 0, 5: 0, 7: 0, 8: 0, 10: 0, 11: 0, 13: 0, 19: 0, 20: 0, 0: 0, 29: 0, 31: 0,
    9: 1, 14: 2, 15: 3, 16: 3, 17: 4, 18: 5, 21: 6, 2: 7, 3: 7, 4: 7, 6: 7, 12: 8,
    22: 9, 23: 10, 24: 11, 25: 12, 26: 13, 27: 14, 28: 15, 30: 16

}


#### mkdir ###
folder_path = data['data_list'][0]['images']['CAM_FRONT']['img_path'].split('samples')[0]+sam_folder+'/'
if not os.path.exists(folder_path): 
    os.mkdir(folder_path) 
for p in ['sam_ground_seg', 'sam_ground_det', 'sam_ground_pth']:
    if not os.path.exists(folder_path+p):
        os.mkdir(folder_path+p)
    for v in VIEWS:
        path = folder_path+p+'/'+v
        if not os.path.exists(path):
            print(f'Make dir: {path}')
            os.mkdir(path) 


#### infer ####
tp, total, pred = 0, 0, 0
gt_p_b, pred_p_b, gt_p_s, pred_p_s = [], [], [], []
missed = []
if part == '1/1':
    start_idx = 0
    end_idx = len(data['data_list'])
else:
    ptarget = int(part.split('/')[0])-1
    ptotal = int(part.split('/')[1])
    num_total = len(data['data_list'])
    start_idx = int(ptarget)*(num_total//int(ptotal))
    end_idx = (int(ptarget)+1)*(num_total//int(ptotal)) 
    if int(ptarget) == int(ptotal)-1:
        end_idx = num_total
        print(f'Last part: {start_idx} to {end_idx}')
print('##############################################')
print(f'Processing from {start_idx} to {end_idx}.')
print('##############################################')
for i in tqdm(range(start_idx, end_idx)):
    # try:
    if True:
        # 1. load point cloud and semantic mask, initialize 
        save_path = res_list[i]['lidar_path'].replace('samples/LIDAR_TOP', f'{sam_folder}/{pred_2d_inst_name}').replace('.pcd.bin', '.h5') 
        
        print(f'$$$$$$$$$$$$$$$ Processing: {save_path} $$$$$$$$$$$$$$$')
        if not os.path.exists(os.path.dirname(save_path)):
            os.makedirs(os.path.dirname(save_path))
        # if os.path.exists(save_path):
        #     print(f'Skip file: {save_path} already exists.')    
        #     continue
        
        if 'lidar_path' in res_list[i].keys():
            lidar_point = load_point(res_list[i]['lidar_path'])
        else:
            lidar_path = res_list[i]['lidar_path']
            lidar_point = load_point(lidar_path)
        dataset_path = data_prefix.get('dataset_path')
        panoptic_path = data['data_list'][i]['pts_panoptic_mask_path']
        panoptic_path = os.path.join(dataset_path, panoptic_path)
        pts_semantic_mask, pts_inst_mask = load_mask(panoptic_path)
        pts_semantic_mask = np.vectorize(label_map.get)(pts_semantic_mask)
        sem_mask = pts_semantic_mask
        sample_token_v = data['data_list'][i]['token']
        # point_coor = lidar_point
        point_coor = np.concatenate((lidar_point, np.ones((lidar_point.shape[0], 1))), axis=1)

        N = lidar_point.shape[0]
        lidar_idx = torch.arange(0, N).to(DEVICE)# view = 'CAM_FRONT_LEFT'
        lidar_cluster = {}
        lidar_cluster_labels = {}
        lidar_cluster_class_scores = {}
        lidar_cluster_mask_scores = {}
        lidar_cluster_classes = {}
        cluster_num = 1
        
        for v in VIEWS: 
            save_seg_path = os.path.join(proj_path, data['data_list'][i]['images'][v]['img_path'].replace('samples', sam_folder+'/sam_ground_seg'))
            save_det_path = os.path.join(proj_path, data['data_list'][i]['images'][v]['img_path'].replace('samples', sam_folder+'/sam_ground_det'))
            save_pth_path = os.path.join(proj_path, data['data_list'][i]['images'][v]['img_path'].replace('samples', sam_folder+'/2d_results').replace('.jpg', '.pth'))
            
            sample_v = nusc.get('sample', sample_token_v)
            pointsensor_token_v = nusc.get('sample_data', sample_v['data']['LIDAR_TOP'])
            point_calibrated_sensor_token_v, point_ego_pose_token_v = pointsensor_token_v['calibrated_sensor_token'], pointsensor_token_v['ego_pose_token']
            cam_token_v = sample_v['data'][v]
            point_cam_coords1, coloring_p, im_p, mask_p = map_pointcloud_to_image_2(
                nusc, point_coor, 
                point_calibrated_sensor_token_v, point_ego_pose_token_v, 
                cam_token_v, min_dist=1.0, return_img=True,
                sem_mask=sem_mask,
                use_label_map=False)
            
            thing_mask = (sem_mask>0)&(sem_mask<11)
            coloring_p[~thing_mask] = [0, 0, 0] # ignore stuffs
            coor = torch.tensor(point_coor[mask_p]).to(DEVICE)
            poi = point_cam_coords1[:, mask_p][:2, :].T.astype(int) # (N, 2)
            poi = torch.tensor(poi).to(DEVICE)
            poi = poi[:,[1,0]]
            
            img_path = res_list[i]['imgs_meta']['img_path'][v]
            image_source, image = load_image(img_path)
            H, W, _ = image_source.shape
            target_bboxes, obj_labels, all_cls_scores = [], [], []
            annotated_frame = image_source.copy()
            
            
            # print('Start inferencing...')
            # project point cloud to camera view
            print('Processing:', save_pth_path)
            if os.path.exists(save_pth_path) and USE_LOCAL_MASK:
                # print(f'Skip file: {save_pth_path} already exists.')
                sam_res = torch.load(save_pth_path)
                sam_masks = sam_res['masks']
                sam_masks = torch.from_numpy(sam_masks).to(DEVICE) if isinstance(sam_masks, np.ndarray) else sam_masks.to(DEVICE)
                sam_masks = expand_mask_torch(sam_masks)
                det_scores = sam_res['class_scores']
                mask_scores = sam_res['mask_scores']
                classes = sam_res['obj_labels']
                assert len(torch.unique(sam_masks))-1 <= det_scores.shape[0], "The number of masks and scores should be the same." # may filter out some masks by nms
                
            else:
                ################# inference #################
                for d, TEXT_PROMPT in enumerate(thing_texts):                
                    
                    # step-1: detection
                    # 1. vanailla DINO
                    if TEXT_PROMPT != "traffic-cone":
                        boxes, logits, phrases = predict(
                            model=groundingdino_model, 
                            image=image, 
                            caption=TEXT_PROMPT, 
                            box_threshold=BOX_TRESHOLD, 
                            text_threshold=TEXT_TRESHOLD,
                            device=DEVICE
                        )
                        if boxes.shape[0] == 0:
                            # print(f"Frame {i}, View {view}: No object detected.")
                            continue

                    else:
                        # 2. dule check with two prompts
                        boxes_cand, logits_cand, phrases_cand = {}, {}, {}
                        for TEXT_PROMPT in dule_texts:
                            boxes, logits, phrases = predict(
                                model=groundingdino_model, 
                                image=image, 
                                caption=TEXT_PROMPT, 
                                box_threshold=BOX_TRESHOLD, 
                                text_threshold=TEXT_TRESHOLD,
                                device=DEVICE
                            )
                            boxes_cand[TEXT_PROMPT] = boxes
                            logits_cand[TEXT_PROMPT] = logits
                            phrases_cand[TEXT_PROMPT] = phrases
                    
                        # # select the overlapped boxes
                        # boxes, logits, phrases = keep_overlaped(boxes_cand[dule_texts[0]], boxes_cand[dule_texts[1]],
                        #                         logits_cand[dule_texts[0]], logits_cand[dule_texts[1]],
                        #                         phrases_cand[dule_texts[0]], phrases_cand[dule_texts[1]],
                        #                         overlap_thre)
                        # save all boxes
                        boxes = torch.cat([boxes_cand[dule_texts[0]], boxes_cand[dule_texts[1]]], dim=0)
                        logits = torch.cat([logits_cand[dule_texts[0]], logits_cand[dule_texts[1]]], dim=0)

                        # 正确构造phrases列表 - 每个box对应一个phrase字符串
                        phrases_0 = phrases_cand[dule_texts[0]]  # 第一个prompt的phrases
                        phrases_1 = phrases_cand[dule_texts[1]]  # 第二个prompt的phrases

                        # 确保phrases是字符串列表，不是嵌套列表
                        if isinstance(phrases_0, list) and len(phrases_0) > 0:
                            phrase_0 = phrases_0[0] if isinstance(phrases_0[0], str) else str(phrases_0[0])
                        else:
                            phrase_0 = str(phrases_0)

                        if isinstance(phrases_1, list) and len(phrases_1) > 0:
                            phrase_1 = phrases_1[0] if isinstance(phrases_1[0], str) else str(phrases_1[0])
                        else:
                            phrase_1 = str(phrases_1)

                        # 为每个box分配对应的phrase
                        phrases = [phrase_0] * boxes_cand[dule_texts[0]].shape[0] + [phrase_1] * boxes_cand[dule_texts[1]].shape[0]
                        if boxes.shape[0] == 0:
                            continue

                    # annotated_frame = annotate(image_source=image_source, boxes=boxes, logits=logits, phrases=phrases)
                    # box: normalized box xywh -> unnormalized xyxy
                    H, W, _ = image_source.shape
                    boxes_xyxy = box_ops.box_cxcywh_to_xyxy(boxes) * torch.Tensor([W, H, W, H])
                    
                    # step-1.5: filter with nms
                    # filter with nms
                    # if '' in phrases: #TODO: not know why
                    #     phrases = [thing_texts[d] for _ in phrases]

                    boxes_xyxy, cls_scores, phrases, keep = filter_boxes(boxes_xyxy, logits, phrases, thing_texts=thing_texts, thre=dino_nms_thresh)
                    boxes = boxes[keep]
                    assert len(boxes) == len(cls_scores) == len(phrases)

                    if len(boxes) == 0:
                        continue
                    
                    if SAVE_IMAGES:
                        # # print each instance bbox, score and class
                        # for box, score, phrase in zip(boxes_xyxy, cls_scores, phrases):
                        #     print(f"Box: {box}, Score: {score}, Phrase: {phrase}")
                        annotated_frame = annotate(image_source=annotated_frame, boxes=boxes, logits=cls_scores, phrases=phrases)
                        annotated_frame = annotated_frame[...,::-1] # BGR to RGB
                    # boxes_xyxy = box_ops.box_cxcywh_to_xyxy(boxes) * torch.Tensor([W, H, W, H])
                    target_bboxes.append(boxes_xyxy)
                    all_cls_scores.append(cls_scores)
                    for _ in range(boxes_xyxy.shape[0]):
                        obj_labels.append(TEXT_PROMPT)
                   
                if SAVE_IMAGES: 
                    plt.figure(figsize=(16, 9))
                    plt.imshow(annotated_frame)
                    plt.axis('off')
                    plt.savefig(save_det_path)
                    # print(f'Save to {save_det_path}')
                    plt.close()

                    if target_bboxes != []:
                        target_bboxes = torch.cat(target_bboxes, dim=0)
                        all_cls_scores = torch.cat(all_cls_scores, dim=0)
                    else:
                        # save the empty results with consistent format
                        res = np.array(image_source)
                        img_rendered_poi = plot_point_in_camview(point_cam_coords1[:, mask_p], coloring_p[mask_p, :], im_p, dot_size=4)

                        # 使用辅助函数保存双subplot图像
                        save_dual_subplot_image(
                            res, img_rendered_poi,
                            'Image (No Detections)', 'Point Cloud Projection on RGB Image',
                            save_seg_path
                        )
                        continue
                else:
                    if target_bboxes != []:
                        target_bboxes = torch.cat(target_bboxes, dim=0)
                        all_cls_scores = torch.cat(all_cls_scores, dim=0)

                # set image
                sam_predictor.set_image(image_source)
                transformed_boxes = sam_predictor.transform.apply_boxes_torch(target_bboxes, image_source.shape[:2]).to(DEVICE)
                masks, mask_scores, mask_logits = sam_predictor.predict_torch(
                            point_coords = None,
                            point_labels = None,
                            boxes = transformed_boxes,
                            multimask_output = False,
                        )

                masks_ = masks.squeeze(1)
                if SAVE_IMAGES:
                    # 生成带mask annotation的图像
                    res_with_masks = plot_anns(pack_anno(masks_, mask_scores, target_bboxes, obj_labels), image_source)

                    # 生成点云投影图像
                    img_rendered_poi = plot_point_in_camview(point_cam_coords1[:, mask_p], coloring_p[mask_p, :], im_p, dot_size=4)

                    # 使用辅助函数保存双subplot图像
                    save_dual_subplot_image(
                        res_with_masks, img_rendered_poi,
                        'Image with Mask Annotations', 'Point Cloud Projection on RGB Image',
                        save_seg_path
                    )
                    
                
                masks_ = masks_.cpu()
                det_scores = all_cls_scores.cpu()  # DINO的classification scores
                mask_scores = mask_scores.cpu()  # SAM的mask quality scores
                mask_logits = mask_logits.cpu()  # SAM的mask logits
                target_bboxes = target_bboxes.cpu()

                # 确保数量一致性
                assert masks_.shape[0] == det_scores.shape[0] == mask_scores.shape[0], \
                    f"Shape mismatch: masks {masks_.shape[0]}, class_scores {det_scores.shape}, mask_scores {mask_scores.shape[0]}"

                # 可选的mask重叠过滤
                if ENABLE_MASK_OVERLAP_FILTER and masks_.shape[0] > 1:
                    filtered_masks, filtered_det_scores, filtered_obj_labels, keep_indices = filter_overlapping_masks(
                        masks_, det_scores, obj_labels, overlap_threshold=MASK_OVERLAP_THRESHOLD
                    )

                    filtered_mask_scores = mask_scores[keep_indices]
                    filtered_mask_logits = mask_logits[keep_indices]
                    filtered_target_bboxes = target_bboxes[keep_indices]

                    masks_ = filtered_masks
                    det_scores = filtered_det_scores
                    mask_scores = filtered_mask_scores
                    mask_logits = filtered_mask_logits
                    target_bboxes = filtered_target_bboxes
                    obj_labels = filtered_obj_labels
                
                debug_save_path = save_pth_path.replace('.pth', '_mask_debug.png')

                # 保存为新格式：mask图像 + COCO JSON
                save_masks_and_annotations_coco_format(
                    masks_, det_scores, mask_scores, target_bboxes, obj_labels,
                    save_pth_path, thing_texts, scale_factor=0.4
                )
                sam_masks = masks_.squeeze(1).to(DEVICE)
                classes = obj_labels

            ######### project to 3D using optimized method #########
            # Use the optimized function to filter 3D points in masks
            obj_poi_coors, obj_poi_idx, obj_poi_labels = filter_3d_points_in_mask_optimized(
                point_coor, poi, sam_masks, mask_p, lidar_idx, sem_mask
            )
            
            # print('*********:', len(obj_poi_coors), det_scores.shape, len(sam_classes))
            for k in range(len(obj_poi_coors)):

                if obj_poi_coors[k].shape[0] == 0:
                    print('No points in mask', k, 'class', classes[k])
                    continue
                lidar_cluster[cluster_num] = obj_poi_idx[k]  # (M,) tensor
                lidar_cluster_labels[cluster_num] = obj_poi_labels[k] # (M,) tensor
                lidar_cluster_class_scores[cluster_num] = det_scores[k]
                lidar_cluster_mask_scores[cluster_num] =  mask_scores[k]
                lidar_cluster_classes[cluster_num] = classes[k]
                cluster_num += 1
                    
        # pack cluster_id and semantic label to point cloud
        pts_inst_cluster = torch.zeros((N)).to(DEVICE)
        pts_inst_cluster_color = torch.zeros((N,3)).to(DEVICE)
        pts_inst_cluster_class_score = torch.zeros((N)).to(DEVICE)
        pts_inst_cluster_mask_score = torch.zeros((N)).to(DEVICE)
        pts_inst_cluster_classes = torch.zeros((N)).to(DEVICE)
        for n in lidar_cluster.keys():
            semantic_label = lidar_cluster_labels[n]
            pts_inst_cluster[lidar_cluster[n].squeeze()] = n
            pts_inst_cluster_class_score[lidar_cluster[n].squeeze()] = lidar_cluster_class_scores[n]
            pts_inst_cluster_mask_score[lidar_cluster[n].squeeze()] = lidar_cluster_mask_scores[n]
            pts_inst_cluster_classes[lidar_cluster[n].squeeze()] = text_labels[lidar_cluster_classes[n]]
            color = torch.zeros((semantic_label.shape[0], 3)).to(DEVICE)
            for idx, p in enumerate(semantic_label):
                color[idx] = torch.tensor(color_map[int(p)])/255
            pts_inst_cluster_color[lidar_cluster[n].squeeze()] = color
        ########################################################################
             
        # save the cluster results
        with h5py.File(save_path, 'w') as f:
            f.create_dataset('mask', data=pts_inst_cluster.cpu().numpy().astype(np.uint16), compression='gzip')
            f.create_dataset('class_score', data=pts_inst_cluster_class_score.cpu().numpy().astype(np.float32), compression='gzip')
            f.create_dataset('mask_score', data=pts_inst_cluster_mask_score.cpu().numpy().astype(np.float32), compression='gzip')
            f.create_dataset('classes', data=pts_inst_cluster_classes.cpu().numpy().astype(np.uint8), compression='gzip')

        ############# CAL P&R ###############
        ## load gt
        if 'pts_instance_mask' in res_list[i].keys():
            pts_instance_mask = res_list[i]['pts_instance_mask']
            pts_semantic_mask = res_list[i]['pts_semantic_mask']
        else:
            pts_semantic_mask, pts_instance_mask = load_mask(panoptic_path)
            pts_semantic_mask = np.vectorize(label_map.get)(pts_semantic_mask) # map to 0-19 classes
        
        gt_pts_inst_thing = pts_instance_mask.copy()
        gt_pts_inst_thing[(pts_semantic_mask==0)|(pts_semantic_mask>10)] = 0
        gt_pts_inst_thing = torch.from_numpy(gt_pts_inst_thing).to(DEVICE)
        
        ## load pred
        pred_pts_inst = torch.from_numpy(pts_inst_cluster).to(DEVICE) if isinstance(pts_inst_cluster, np.ndarray) else pts_inst_cluster.to(DEVICE)
        print(torch.unique(pred_pts_inst))
        recall = cand_recall_calc(gt_pts_inst_thing, pred_pts_inst)
        tp += recall.sum().float()
        pred += len(torch.unique(pred_pts_inst))-1
        total += recall.shape[0]

print('$'*20)
print('precision dscore:', tp/pred, tp.cpu().numpy(), '/', pred)
print('recall dscore:', tp/total, tp.cpu().numpy(), '/', total)
