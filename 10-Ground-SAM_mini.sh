# # for Grounding-DINO+SAM
# # move main.py to tools/Grounded-Segment-Anything
# cp Grouding_SAM_Main.py tools/Grounded-Segment-Anything/
# cp Grouding_SAM_Utils.py tools/Grounded-Segment-Anything/

# conda activate dino
# cd /home/<USER>/sutd/zhoutaow/scratch/codefield/3dmmpano
# source ~/.conda/envs/bin/activate 
# CUDA_VISIBLE_DEVICES=0 \
# python Grouding_SAM_Postprocess_HTC.py --data_mode trainval --dset val --part '1/1' --model maskrcnn --use_bbox 

CUDA_VISIBLE_DEVICES=0 \
python Grouding_SAM_Main_UDA.py --data_mode mini --dset val --part '1/1'