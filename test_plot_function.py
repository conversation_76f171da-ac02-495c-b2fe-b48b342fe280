#!/usr/bin/env python3
"""
测试 plot_point_in_camview 函数修复
"""

import numpy as np
import matplotlib.pyplot as plt
import sys
import os

# 添加当前目录到路径
sys.path.append('.')

def test_plot_function():
    """测试修复后的 plot_point_in_camview 函数"""
    
    from Grouding_SAM_Utils import plot_point_in_camview
    
    print("测试 plot_point_in_camview 函数...")
    
    # 创建测试数据
    # 模拟图像
    im = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
    
    # 模拟点云坐标 (2, N) 格式 - 匹配原始调用
    N = 100
    points = np.random.rand(2, N) * np.array([[640], [480]])  # (2, N)
    
    # 模拟颜色
    coloring = np.random.rand(N, 3)  # (N, 3)
    
    print(f"输入数据:")
    print(f"  im shape: {im.shape}")
    print(f"  points shape: {points.shape}")
    print(f"  coloring shape: {coloring.shape}")
    
    try:
        # 调用函数
        result = plot_point_in_camview(points, coloring, im, dot_size=4)
        
        print(f"\n结果:")
        print(f"  result type: {type(result)}")
        print(f"  result shape: {result.shape}")
        print(f"  result dtype: {result.dtype}")
        
        # 验证结果是numpy array
        assert isinstance(result, np.ndarray), f"Expected numpy array, got {type(result)}"
        assert len(result.shape) == 3, f"Expected 3D array, got shape {result.shape}"
        assert result.shape[2] == 3, f"Expected RGB image, got shape {result.shape}"
        
        print("✅ 函数测试通过！")
        
        # 保存结果图像用于验证
        os.makedirs("debug_output", exist_ok=True)
        plt.imsave("debug_output/test_plot_result.png", result)
        print("✅ 结果图像已保存到 debug_output/test_plot_result.png")
        
        return True
        
    except Exception as e:
        print(f"❌ 函数测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_with_different_formats():
    """测试不同的输入格式"""
    
    from Grouding_SAM_Utils import plot_point_in_camview
    
    print("\n测试不同的输入格式...")
    
    # 创建测试数据
    im = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
    
    # 测试 (2, N) 格式
    points_2N = np.random.rand(2, 50) * 100
    coloring = np.random.rand(50, 3)
    
    try:
        result1 = plot_point_in_camview(points_2N, coloring, im)
        print(f"✅ (2, N) 格式测试通过: {result1.shape}")
    except Exception as e:
        print(f"❌ (2, N) 格式测试失败: {e}")
    
    # 测试 (N, 2) 格式
    points_N2 = np.random.rand(50, 2) * 100
    
    try:
        result2 = plot_point_in_camview(points_N2, coloring, im)
        print(f"✅ (N, 2) 格式测试通过: {result2.shape}")
    except Exception as e:
        print(f"❌ (N, 2) 格式测试失败: {e}")

if __name__ == "__main__":
    success = test_plot_function()
    test_with_different_formats()
    
    if success:
        print("\n🎉 所有测试通过！函数修复成功。")
    else:
        print("\n💥 测试失败，需要进一步调试。")
