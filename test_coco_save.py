#!/usr/bin/env python3
"""
测试COCO格式保存功能
"""

import torch
import numpy as np
import json
import os
import sys
from PIL import Image

# 添加当前目录到路径
sys.path.append('.')

def create_test_data():
    """创建测试数据"""
    
    # 创建测试masks
    h, w = 480, 640
    n_masks = 3
    
    masks = torch.zeros((n_masks, h, w), dtype=torch.bool)
    
    # Mask 1: 左上角
    masks[0, 50:150, 50:150] = True
    
    # Mask 2: 右上角  
    masks[1, 50:150, 400:500] = True
    
    # Mask 3: 中心
    masks[2, 200:300, 250:350] = True
    
    # 创建其他数据
    class_scores = torch.tensor([0.9, 0.8, 0.7])
    mask_scores = torch.tensor([[0.85], [0.75], [0.65]])
    bboxes = torch.tensor([
        [50, 50, 150, 150],
        [400, 50, 500, 150],
        [250, 200, 350, 300]
    ], dtype=torch.float32)
    obj_labels = ['car', 'truck', 'bus']
    thing_texts = ['car', 'truck', 'bus', 'traffic-cone']
    
    return masks, class_scores, mask_scores, bboxes, obj_labels, thing_texts

def test_coco_save():
    """测试COCO格式保存功能"""
    
    from Grouding_SAM_Main_UDA import save_masks_and_annotations_coco_format
    
    print("测试COCO格式保存功能...")
    
    # 创建测试数据
    masks, class_scores, mask_scores, bboxes, obj_labels, thing_texts = create_test_data()
    
    print(f"测试数据:")
    print(f"  masks shape: {masks.shape}")
    print(f"  class_scores: {class_scores}")
    print(f"  mask_scores: {mask_scores.squeeze()}")
    print(f"  obj_labels: {obj_labels}")
    print(f"  thing_texts: {thing_texts}")
    
    # 确保输出目录存在
    os.makedirs("debug_output", exist_ok=True)
    
    try:
        # 测试保存功能
        save_path = "debug_output/test_sample.pth"
        save_masks_and_annotations_coco_format(
            masks, class_scores, mask_scores, bboxes, obj_labels,
            save_path, thing_texts, scale_factor=0.5
        )
        
        print("✅ 保存功能测试成功！")
        
        # 验证保存的文件
        mask_path = "debug_output/test_sample_masks.png"
        json_path = "debug_output/test_sample_annotations.json"
        
        # 检查mask图像
        if os.path.exists(mask_path):
            mask_img = Image.open(mask_path)
            mask_array = np.array(mask_img)
            print(f"✅ Mask图像已保存: {mask_path}")
            print(f"  - 图像尺寸: {mask_array.shape}")
            print(f"  - 唯一值: {np.unique(mask_array)}")
        else:
            print(f"❌ Mask图像未找到: {mask_path}")
            return False
        
        # 检查JSON文件
        if os.path.exists(json_path):
            with open(json_path, 'r') as f:
                coco_data = json.load(f)
            
            print(f"✅ JSON文件已保存: {json_path}")
            print(f"  - 图像信息: {coco_data['images'][0]}")
            print(f"  - 类别数量: {len(coco_data['categories'])}")
            print(f"  - 标注数量: {len(coco_data['annotations'])}")
            
            # 显示每个annotation的详细信息
            for ann in coco_data['annotations']:
                print(f"  - Instance {ann['id']}: {ann['class_name']} "
                      f"(class_score: {ann['class_score']:.3f}, "
                      f"mask_score: {ann['mask_score']:.3f}, "
                      f"area: {ann['area']})")
        else:
            print(f"❌ JSON文件未找到: {json_path}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_load_and_visualize():
    """测试加载和可视化保存的数据"""
    
    print("\n测试加载和可视化...")
    
    mask_path = "debug_output/test_sample_masks.png"
    json_path = "debug_output/test_sample_annotations.json"
    
    if not (os.path.exists(mask_path) and os.path.exists(json_path)):
        print("❌ 测试文件不存在，请先运行保存测试")
        return False
    
    try:
        # 加载mask图像
        mask_img = Image.open(mask_path)
        mask_array = np.array(mask_img)
        
        # 加载JSON数据
        with open(json_path, 'r') as f:
            coco_data = json.load(f)
        
        # 创建可视化
        import matplotlib.pyplot as plt
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 显示instance mask
        ax1.imshow(mask_array, cmap='tab20')
        ax1.set_title('Instance Masks')
        ax1.axis('off')
        
        # 显示带标注的mask
        ax2.imshow(mask_array, cmap='tab20')
        
        # 添加bbox和标签
        for ann in coco_data['annotations']:
            bbox = ann['bbox']
            x1, y1, x2, y2 = bbox
            
            # 绘制bbox
            rect = plt.Rectangle((x1, y1), x2-x1, y2-y1, 
                               fill=False, edgecolor='red', linewidth=2)
            ax2.add_patch(rect)
            
            # 添加标签
            label = f"{ann['class_name']}\nCS:{ann['class_score']:.2f}\nMS:{ann['mask_score']:.2f}"
            ax2.text(x1, y1-10, label, fontsize=8, 
                    bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7))
        
        ax2.set_title('Masks with Annotations')
        ax2.axis('off')
        
        plt.tight_layout()
        plt.savefig("debug_output/test_visualization.png", dpi=150, bbox_inches='tight')
        plt.close()
        
        print("✅ 可视化测试成功！")
        print("✅ 可视化结果已保存到: debug_output/test_visualization.png")
        
        return True
        
    except Exception as e:
        print(f"❌ 可视化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success1 = test_coco_save()
    success2 = test_load_and_visualize()
    
    if success1 and success2:
        print("\n🎉 所有测试通过！COCO格式保存功能正常工作。")
        print("请查看 debug_output/ 目录中的文件：")
        print("  - test_sample_masks.png: Instance mask图像")
        print("  - test_sample_annotations.json: COCO格式标注")
        print("  - test_visualization.png: 可视化结果")
    else:
        print("\n💥 部分测试失败，需要进一步调试。")
