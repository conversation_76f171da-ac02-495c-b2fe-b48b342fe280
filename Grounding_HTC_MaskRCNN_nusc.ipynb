import os
import sys
import numpy as np
from tqdm import tqdm
import torch
import cv2
import h5py
import pickle
import matplotlib.pyplot as plt
from typing import List, Optional, Sequence, Tuple, Union

# import mmcv
# from mmcv.transforms import BaseTransform
# from mmdet3d.registry import TRANSFORMS
# from mmdet3d.structures.points import BasePoints

# from tools.general_tool import load_point, load_mask, load_viewimages_meta, load_viewimages
# from tools.pkl_utils import load_data_skitti, load_viewimages_skitti
from tools.pkl_utils import load_viewimages_path
from tools.pkl_utils import load_point_mask_viewimages
from tools.projection.pc2img import map_pointcloud_to_image_2
from tools.projection.pc2img import proj_lidar2img
from tools.projection.img_aug import merge_images_yaw, fit_box_cv, expand_box, crop_box_img, paste_box_img, merge_images_pitch_torch, fit_to_box
from tools.projection.pc2img import get_lidar2img

from tools.projection.pc2img import pack_lidar2imgs #, pack_lidar2imgs_skitti
from tools.general_tool import load_point, load_mask, load_viewimages
# from tools.pkl_utils import get_image_metas
# from tools.heatmap_convertor import cand_recall_calc
from tools.cluster_utils import create_mask, create_mask_ref, cluster_points_with_dbscan, get_comfusion_matrix
sys.path.append('tool-dino/Grounded-Segment-Anything')
from Grouding_SAM_Utils import PointCloudVisualizer


from nuscenes.nuscenes import NuScenes
from tools.projection.pc2img import _LidarPointCloud
from pyquaternion import Quaternion

proj_path = os.getcwd()
dataroot = proj_path+'/data/nuscenes_mini'
pklfile = os.path.join(dataroot, 'nuscenes_infos_train_1.pkl')

with open(pklfile, 'rb') as f:
    data = pickle.load(f)

res_list = load_point_mask_viewimages(data, dataroot)
nusc = NuScenes(version=f'v1.0-mini', dataroot=dataroot, verbose=True)


color_map = {
            0: [0, 0, 0],  # noise                 black
            1: [255, 120, 50],  # barrier               orange
            2: [255, 192, 203],  # bicycle               pink
            3: [255, 255, 0],  # bus                   yellow
            4: [0, 150, 245],  # car                   blue
            5: [0, 255, 255],  # construction_vehicle  cyan
            6: [255, 127, 0],  # motorcycle            dark orange
            7: [255, 0, 0],  # pedestrian            red
            8: [255, 240, 150],  # traffic_cone          light yellow
            9: [135, 60, 0],  # trailer               brown
            10: [160, 32, 240],  # truck                 purple
            11: [255, 0, 255],  # driveable_surface     dark pink
            12: [139, 137, 137],  # other_flat            dark red
            13: [75, 0, 75],  # sidewalk              dark purple
            14: [150, 240, 80],  # terrain               light green
            15: [230, 230, 250],  # manmade               white
            16: [0, 175, 0],  # vegetation            green
        }   

label_map = {
    1: 0, 5: 0, 7: 0, 8: 0, 10: 0, 11: 0, 13: 0, 19: 0, 20: 0, 0: 0, 29: 0, 31: 0,
    9: 1, 14: 2, 15: 3, 16: 3, 17: 4, 18: 5, 21: 6, 2: 7, 3: 7, 4: 7, 6: 7, 12: 8,
    22: 9, 23: 10, 24: 11, 25: 12, 26: 13, 27: 14, 28: 15, 30: 16

}

VIEWS = ['CAM_FRONT', 'CAM_FRONT_RIGHT', 'CAM_FRONT_LEFT', 'CAM_BACK', 'CAM_BACK_LEFT', 'CAM_BACK_RIGHT']
thing_classes = ['barrier', 'bicycle', 'bus', 'car', 'construction_vehicle', 'motorcycle', 'pedestrian', 'traffic_cone', 'trailer', 'truck']


for i in tqdm(range(len(res_list))):
    save_path = res_list[i]['lidar_path'].replace('samples/LIDAR_TOP', 'gsam_uda/pred_inst_pts_xcluster').replace('.pcd.bin', '.h5') 
    # if not os.path.exists(os.path.dirname(save_path)):
    #     os.makedirs(os.path.dirname(save_path))
    lidar_point = load_point(res_list[i]['lidar_path'])
    res_list[i]['points'] = lidar_point
    panoptic_path = data['data_list'][i]['pts_panoptic_mask_path']
    panoptic_path = os.path.join(dataroot, panoptic_path)
    pts_semantic_mask, pts_inst_mask = load_mask(panoptic_path)
    pts_semantic_mask = np.vectorize(label_map.get)(pts_semantic_mask)
    res_list[i]['pts_semantic_mask'] = pts_semantic_mask
    res_list[i]['pts_instance_mask'] = pts_inst_mask
    sem_mask = pts_semantic_mask
    # load h5py
    with h5py.File(save_path, 'r') as f:
        pts_inst_cluster = f['mask'][:]
    res_list[i]['pred_inst'] = pts_inst_cluster


visualizer = PointCloudVisualizer(res_list[0], color_map, dataset_type='nuscenes')
visualizer.plot(color_mode='instance', mode='2d', filter_stuffs=True)
visualizer.plot(color_mode='pred_instance', mode='2d', filter_stuffs=False)
visualizer.plot(color_mode='semantic_thing', mode='2d', filter_stuffs=True)


class PointCloudVisualizer:
    def __init__(self, out_dict, color_map, save_path='output/rerun_samples/', dataset_type='nuscenes'):
        self.points = out_dict['points'].copy()
        if 'pts_semantic_mask' in out_dict:
            self.sem_mask = out_dict['pts_semantic_mask'].copy()
        if 'augment_mask' in out_dict:
            self.aug_mask = out_dict['augment_mask'].copy()
        if 'pts_instance_mask' in out_dict:
            self.inst_mask = out_dict['pts_instance_mask'].copy()
        if 'pred_inst' in out_dict:
            self.pred_inst = out_dict['pred_inst'].copy()
        self.color_map = color_map
        self.save_path = save_path
        if dataset_type == 'semantickitti':
            self.ignore_class = 19
            self.thing_classes = range(8)
        elif dataset_type == 'nuscenes':
            self.ignore_class = 0
            self.thing_classes = range(1, 11)

    def prepare_semantic_colors(self):
        return np.array([self.color_map[i] for i in self.sem_mask]) / 255
    
    def prepare_semantic_thing_colors(self):
        sem_mask_thing = self.sem_mask.copy()
        # nusc
        # sem_mask_thing[(sem_mask_thing==0) | (sem_mask_thing>=10)] = 0
        # skitti
        # print(f'NOTE: Skitti semantic label is different from nuscenes. Please check the label mapping.')
        sem_mask_thing[~np.isin(sem_mask_thing, self.thing_classes)] = self.ignore_class
        return np.array([self.color_map[i] for i in sem_mask_thing]) / 255

    def prepare_augment_colors(self):
        aug_color = np.array([[255, 0, 0], [0, 0, 255]]) # 0: red, 1: blue
        aug_mask = self.aug_mask.astype(np.int8)
        return aug_color[aug_mask] / 255

    def prepare_instance_colors(self, inst_mask, filter_stuffs=False):
        coloring_inst = np.ones((inst_mask.shape[0], 3))
        # colorize the background to light gray
        coloring_inst = coloring_inst* np.array([200, 200, 200]) / 255
        num_inst = 0
        for inst_id in np.unique(inst_mask):
            if inst_id == 0 or (filter_stuffs and np.unique(self.sem_mask[inst_mask == inst_id])[0] not in self.thing_classes):
                # print(f'Ignore instance {inst_id}, semantic label: {np.unique(self.sem_mask[inst_mask == inst_id])[0]} due to {inst_id == 0} or {np.unique(self.sem_mask[inst_mask == inst_id])[0] not in self.thing_classes')
                continue
            # print(f'Instance {inst_id} has {np.sum(inst_mask == inst_id)} points, semantic label: {np.unique(self.sem_mask[inst_mask == inst_id])[0]}')
            coloring_inst[inst_mask == inst_id] = np.random.randint(0, 255, 3) / 255
            num_inst += 1
        return coloring_inst, num_inst

    def save_data(self):
        np.save(self.save_path + 'points.npy', self.points)
        np.save(self.save_path + 'sem_mask.npy', self.sem_mask)
        np.save(self.save_path + 'aug_mask.npy', self.aug_mask)

    def plot(self, color_mode='instance', mode='2d', custom_input=None, filter_stuffs=False):
        assert mode in ['2d', '3d'], "Invalid mode. Choose from '2d' or '3d'."
        if color_mode == 'semantic':
            colors = self.prepare_semantic_colors()
        elif color_mode == 'semantic_thing':
            colors = self.prepare_semantic_thing_colors()
        elif color_mode == 'augment':
            colors = self.prepare_augment_colors()
        elif color_mode == 'instance':
            colors, num_inst = self.prepare_instance_colors(self.inst_mask, filter_stuffs)
            print(f'Number of instances: {num_inst}')
        elif color_mode == 'pred_instance':
            colors, num_inst = self.prepare_instance_colors(self.pred_inst, filter_stuffs)
            print(f'Number of instances: {num_inst}')
        elif color_mode == 'custom':
            colors, num_inst = self.prepare_instance_colors(custom_input, filter_stuffs)
            print(f'Number of instances: {num_inst}')
        else:
            raise ValueError("Invalid color_mode. Choose from 'semantic', 'augment', or 'instance'.")

        fig = plt.figure(figsize=(10, 10))
        if mode == '2d':
            fig = plt.figure(figsize=(10, 10))
            ax = fig.add_subplot(111)
            ax.scatter(self.points[:, 0], self.points[:, 1], c=colors, s=1)
            ax.set_xlabel('X Label')
            ax.set_ylabel('Y Label')
        else:
            ax = fig.add_subplot(111, projection=mode, proj_type='ortho')
            ax.scatter(self.points[:, 0], self.points[:, 1], self.points[:, 2], c=colors, s=3)
            ax.set_xlabel('X Label')
            ax.set_ylabel('Y Label')
            ax.set_zlabel('Z Label')
        plt.show()


DEVICE = torch.device('cuda:3' if torch.cuda.is_available() else 'cpu')
DO_CLUSTER = False
USE_BBOX = True

def lift_instance_mask(instance_mask, include_background=False):
    """
    将 [H, W] 的 instance mask 提升为 [N, H, W] 的 binary mask。

    Args:
        instance_mask (ndarray): [H, W] 的 numpy array，每个像素为 instance id。
        include_background (bool): 是否包含背景（id==0）。

    Returns:
        binary_masks (ndarray): [N, H, W] 的 binary array，第 i 个 channel 对应第 i 个 instance。
        instance_ids (ndarray): [N] 的 array，对应每个 channel 的 instance id。
    """
    unique_ids = np.unique(instance_mask)
    if not include_background:
        unique_ids = unique_ids[unique_ids != 0]

    binary_masks = (instance_mask[None, :, :] == unique_ids[:, None, None]).astype(np.uint8)
    return binary_masks, unique_ids

def mask_to_bbox_mask(instance_mask):
    """
    将instance mask转换为bbox mask。

    参数:
        instance_mask (np.ndarray): 输入的h*w mask，背景为0，不同instance用不同正整数标记。

    返回:
        bbox_mask (np.ndarray): h*w数组，每个实例区域被其bbox覆盖，值与原instance ID一致。
    """
    h, w = instance_mask.shape
    bbox_mask = np.zeros((h, w), dtype=instance_mask.dtype)
    instance_ids = np.unique(instance_mask)
    instance_ids = instance_ids[instance_ids != 0]  # 去掉背景

    for inst_id in instance_ids:
        y_indices, x_indices = np.where(instance_mask == inst_id)
        if len(y_indices) == 0 or len(x_indices) == 0:
            continue  # 跳过空mask
        y_min, y_max = y_indices.min(), y_indices.max()
        x_min, x_max = x_indices.min(), x_indices.max()
        bbox_mask[y_min:y_max+1, x_min:x_max+1] = inst_id

    return bbox_mask


missed = []
for i in tqdm(range(1)):
    # 1. load point cloud and semantic mask, initialize 
    save_path = res_list[i]['lidar_path'].replace('samples/LIDAR_TOP', 'htc/pred_inst_pts').replace('.pcd.bin', '.h5') 
    if not os.path.exists(os.path.dirname(save_path)):
        os.makedirs(os.path.dirname(save_path))
    # if os.path.exists(save_path):
    #     continue
    lidar_point = load_point(res_list[i]['lidar_path'])
    res_list[i]['points'] = lidar_point
    dataset_path = data_root
    panoptic_path = data['data_list'][i]['pts_panoptic_mask_path']
    panoptic_path = os.path.join(dataset_path, panoptic_path)
    pts_semantic_mask, pts_inst_mask = load_mask(panoptic_path)
    pts_semantic_mask = np.vectorize(label_map.get)(pts_semantic_mask)
    res_list[i]['pts_semantic_mask'] = pts_semantic_mask
    res_list[i]['pts_instance_mask'] = pts_inst_mask
    sem_mask = pts_semantic_mask
    sample_token_v = data['data_list'][i]['token']
    point_coor = np.concatenate((lidar_point, np.ones((lidar_point.shape[0], 1))), axis=1)

    N = lidar_point.shape[0]
    lidar_idx = torch.arange(0, N).to(DEVICE)# view = 'CAM_FRONT_LEFT'
    lidar_cluster = {}
    lidar_cluster_labels = {}
    cluster_num = 1
    pts_inst_cluster = torch.zeros((N)).to(DEVICE)
    pts_inst_cluster_color = torch.zeros((N,3)).to(DEVICE)

    for v,view in enumerate(VIEWS):

        # project point cloud to camera view
        sample_v = nusc.get('sample', sample_token_v)
        pointsensor_token_v = nusc.get('sample_data', sample_v['data']['LIDAR_TOP'])
        point_calibrated_sensor_token_v, point_ego_pose_token_v = pointsensor_token_v['calibrated_sensor_token'], pointsensor_token_v['ego_pose_token']
        cam_token_v = sample_v['data'][view]
        point_cam_coords1, coloring_p, im_p, mask_p = map_pointcloud_to_image_2(
            nusc, point_coor, 
            point_calibrated_sensor_token_v, point_ego_pose_token_v, 
            cam_token_v, min_dist=1.0, return_img=True,
            sem_mask=sem_mask,
            use_label_map=False)

        coor = torch.tensor(point_coor[mask_p]).to(DEVICE)
        poi = point_cam_coords1[:, mask_p][:2, :].T.astype(int) # (N, 2)
        poi = torch.tensor(poi).to(DEVICE)
        poi = poi[:,[1,0]]
        # img_path = res_list[i]['imgs_meta']['img_path'][view]
        frame_folder = os.path.join(data_root, 'htc/img_masks', sample_token_v)
        inst_masks = []
        show_masks = []
        for cls in thing_classes:
            mask_path = os.path.join(frame_folder, f'{v}_{cls}.png')
            if not os.path.exists(mask_path):
                missed.append(res_list[i]['lidar_path'])
                print(f'Missed: {mask_path}')   
                continue
            mask = cv2.imread(mask_path, -1)
            if USE_BBOX:
               mask = mask_to_bbox_mask(mask) 
            show_masks.append(mask)
            inst_masks.append(mask)
        inst_masks = np.stack(inst_masks, axis=0)
        inst_masks = inst_masks.max(0)
        # plt.imshow(inst_masks)
        # plt.show()
        sam_masks, ids = lift_instance_mask(inst_masks)
        sam_masks = torch.tensor(sam_masks).to(DEVICE)
        
        W, H = 1600, 900
        # if use_depth:
        #     depth = depth.unsqueeze(1).type_as(poi)
        #     poi = torch.cat((poi, depth), dim=1)
        poi_mask = create_mask(poi, H, W) #TODO-YINING: merge the two functions
        poi_3d_coor = create_mask_ref(poi, H, W, coor)
        sem_label = torch.tensor(sem_mask[mask_p]).to(DEVICE)
        poi_label = create_mask_ref(poi, H, W, sem_label)
        obj_poi_masks = torch.einsum('nhw,hw->nhw', sam_masks, poi_mask)
        obj_poi_coors = torch.einsum('nhw,hwc->nhwc', obj_poi_masks, poi_3d_coor)
        obj_sem_labels = torch.einsum('nhw,hwc->nhwc', obj_poi_masks, poi_label)

        # get corresponding lidar idx and semantic label
        lidar_idx_1 = lidar_idx[mask_p]
        poi_3d_idx = create_mask_ref(poi, H, W, lidar_idx_1)
        obj_poi_idx = torch.einsum('nhw,hwc->nhwc', sam_masks, poi_3d_idx)
        lidar_label = sem_mask[mask_p] 
        lidar_label = torch.tensor(lidar_label).to(DEVICE) 
        poi_sem_label = create_mask_ref(poi, H, W, lidar_label)
        obj_poi_labels = torch.einsum('nhw,hwc->nhwc', obj_poi_masks, poi_sem_label)
        
        if DO_CLUSTER:
            for k in range(obj_poi_coors.shape[0]):
                sample = obj_poi_coors[k]
                sample_ = sample[torch.where(sample[:, :, 0] != 0)]
                sample_label = obj_sem_labels[k]
                sample_label = sample_label[torch.where(sample[:, :, 0] != 0)].squeeze(1)
                sample_3d_idx = obj_poi_idx[k][torch.where(sample[:, :, 0] != 0)]
                sample_label = obj_poi_labels[k][torch.where(sample[:, :, 0] != 0)].squeeze(1)
                sample = sample_

                # cluster the object coors
                if sample.shape[0] == 0:
                    continue
                clustered_res, clustered_labels = cluster_points_with_dbscan(sample.cpu().numpy(), eps=0.8, min_samples=1)  
                # split the sample_idx into clusters by labels by scatter
                
                for j in range(np.unique(clustered_labels).shape[0]):
                    label = np.unique(clustered_labels)[j]
                    # if label not in lidar_cluster:
                    lidar_cluster[cluster_num] = sample_3d_idx[clustered_labels==label]
                    lidar_cluster_labels[cluster_num] = sample_label[clustered_labels==label]
                    cluster_num += 1
        else:
            for n in range(obj_poi_coors.shape[0]):
                sample = obj_poi_coors[n]
                sample_ = sample[torch.where(sample[:, :, 0] != 0)]
                sample_label = obj_sem_labels[n]
                sample_label = sample_label[torch.where(sample[:, :, 0] != 0)].squeeze(1)
                sample_3d_idx = obj_poi_idx[n][torch.where(sample[:, :, 0] != 0)]
                sample_label = obj_poi_labels[n][torch.where(sample[:, :, 0] != 0)].squeeze(1)
                sample = sample_
                if sample.shape[0] == 0:
                    continue
                lidar_cluster[cluster_num] = sample_3d_idx
                lidar_cluster_labels[cluster_num] = sample_label
                cluster_num += 1
                
    for n in lidar_cluster.keys():
        semantic_label = lidar_cluster_labels[n]
        pts_inst_cluster[lidar_cluster[n].squeeze()] = n
        color = torch.zeros((semantic_label.shape[0], 3)).to(DEVICE)
        for idx, p in enumerate(semantic_label):
            color[idx] = torch.tensor(color_map[int(p)])/255
        pts_inst_cluster_color[lidar_cluster[n].squeeze()] = color
        
        res_list[i]['pred_inst'] = pts_inst_cluster.cpu().numpy()
    # save the cluster results
    print(f'Current skip saving')
    # with h5py.File(save_path, 'w') as f:
    #     f.create_dataset('mask', data=pts_inst_cluster.cpu().numpy().astype(np.uint16), compression='gzip')

    # # quality evaluation: recall
    # ## get the point-level mask
    # select_inst = (sem_mask>0) & (sem_mask<=10)
    # gt_point_bin = select_inst[select_inst>0]
    # gt_point_sem = sem_mask[select_inst>0]
    # pred = (pts_inst_cluster.cpu().numpy()>0) * sem_mask
    # pred_point_sem = pred[select_inst>0]
    # pred_point_bin = pred_point_sem>0

    # gt_p_b.append(gt_point_bin)
    # pred_p_b.append(pred_point_bin)
    # gt_p_s.append(gt_point_sem) 
    # pred_p_s.append(pred_point_sem)

    # ## get the instance-level mask
    # gt_i, pred_i = [], []
    # # pts_inst_mask = res_list[i]['pts_instance_mask']
    # gt_inst_ = torch.tensor(pts_inst_mask).to(DEVICE)
    # M = np.unique(pts_inst_mask).shape[0] # number of instances
    # pred_inst_i = torch.zeros((M)).to(DEVICE)
    # for idx, p in enumerate(np.unique(pts_inst_mask)): # re-index
    #     gt_inst_[gt_inst_==p] = idx
    # gt_inst_onehot = torch.zeros((gt_inst_.shape[0], M)).to(DEVICE)
    # gt_inst_onehot.scatter_(1, gt_inst_.unsqueeze(1), 1)
    # # pred_inst_ = torch.argmax(torch.einsum('pm,p->pm', gt_inst_onehot, pts_inst_cluster), dim=0)>0
    # pred_inst_ = (torch.einsum('pm,p->pm', gt_inst_onehot, pts_inst_cluster)).max(dim=0)[0]>0

    # gt_i.append(np.ones((M), dtype=bool))
    # pred_i.append(pred_inst_.cpu().numpy())


# display the mask 
for img in show_masks:
    if np.unique(img).shape[0] == 1:
        continue
    print(np.unique(img))
    plt.imshow(img)

    plt.show()

view_gt = pts_inst_mask.copy()
view_pred = pts_inst_cluster.cpu().numpy().copy()
recall_res = cand_recall_calc(view_gt, view_pred)
recall_value = recall_res.sum()/recall_res.shape[0]
precision_value = recall_res.sum()/np.unique(view_pred).shape[0]    
print(f'Recall: {recall_value}, Precision: {precision_value}')

visualizer = PointCloudVisualizer(res_list[0], color_map, dataset_type='nuscenes')
visualizer.plot(color_mode='instance', mode='2d', filter_stuffs=True)
visualizer.plot(color_mode='pred_instance', mode='2d', filter_stuffs=True)
visualizer.plot(color_mode='semantic_thing', mode='2d', filter_stuffs=True)