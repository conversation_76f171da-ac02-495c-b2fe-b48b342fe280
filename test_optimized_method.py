#!/usr/bin/env python3
"""
测试优化后的3D点筛选方法
"""
import torch
import numpy as np

def filter_3d_points_in_mask_optimized(points_3d, poi, sam_mask, view_mask, lidar_idx, sem_mask):
    """
    优化版本：筛选落在 2D mask 中的 3D 点，使用向量化操作提高效率
    """
    poi = poi.round().to(torch.int64)
    n, h, w = sam_mask.shape
    N = points_3d.shape[0]
    
    # 获取在当前视角可见的点的索引和数据
    visible_points_3d = points_3d[view_mask]  # (P, 3)
    visible_lidar_idx = lidar_idx[view_mask]  # (P,)
    visible_sem_mask = torch.tensor(sem_mask[view_mask]).to(points_3d.device)  # (P,)
    
    # 向量化处理所有masks - 一次性获取所有mask在所有投影点位置的值
    mask_values = sam_mask[:, poi[:, 1], poi[:, 0]]  # shape: (n, P)
    
    obj_poi_coors = []
    obj_poi_idx = []
    obj_poi_labels = []
    
    for i in range(n):
        # 找到当前mask中值为1的点
        mask_indices = mask_values[i] == 1
        
        # 提取对应的3D坐标、索引和语义标签
        filtered_coors = visible_points_3d[mask_indices]  # (M, 3)
        filtered_idx = visible_lidar_idx[mask_indices]    # (M,)
        filtered_labels = visible_sem_mask[mask_indices]  # (M,)
        
        obj_poi_coors.append(filtered_coors)
        obj_poi_idx.append(filtered_idx)
        obj_poi_labels.append(filtered_labels)
    
    return obj_poi_coors, obj_poi_idx, obj_poi_labels

def test_optimized_method():
    """测试优化方法"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 创建测试数据
    N = 1000  # 总点数
    P = 200   # 可见点数
    H, W = 900, 1600  # 图像尺寸
    n = 3     # mask数量
    
    # 生成测试数据
    points_3d = torch.randn(N, 3).to(device)
    view_mask = torch.zeros(N, dtype=torch.bool).to(device)
    view_mask[:P] = True  # 前P个点可见
    
    # 生成投影坐标 (确保在图像范围内)
    poi = torch.randint(0, min(H, W), (P, 2)).to(device)
    poi[:, 0] = torch.clamp(poi[:, 0], 0, W-1)  # x坐标
    poi[:, 1] = torch.clamp(poi[:, 1], 0, H-1)  # y坐标
    
    # 生成SAM masks
    sam_mask = torch.randint(0, 2, (n, H, W)).to(device)
    
    # 生成其他数据
    lidar_idx = torch.arange(N).to(device)
    sem_mask = np.random.randint(0, 10, N)
    
    print(f"测试数据:")
    print(f"  总点数: {N}")
    print(f"  可见点数: {P}")
    print(f"  图像尺寸: {H}x{W}")
    print(f"  SAM mask数量: {n}")
    
    # 测试优化方法
    try:
        obj_poi_coors, obj_poi_idx, obj_poi_labels = filter_3d_points_in_mask_optimized(
            points_3d, poi, sam_mask, view_mask, lidar_idx, sem_mask
        )
        
        print(f"\n优化方法测试成功!")
        print(f"  返回的mask数量: {len(obj_poi_coors)}")
        for i, (coors, idx, labels) in enumerate(zip(obj_poi_coors, obj_poi_idx, obj_poi_labels)):
            print(f"  Mask {i}: {coors.shape[0]} 个点")
            
        return True
        
    except Exception as e:
        print(f"优化方法测试失败: {e}")
        return False

if __name__ == "__main__":
    success = test_optimized_method()
    if success:
        print("\n✅ 所有测试通过!")
    else:
        print("\n❌ 测试失败!")
