#!/usr/bin/env python3
"""
测试双subplot保存功能
"""

import numpy as np
import matplotlib.pyplot as plt
import sys
import os

# 添加当前目录到路径
sys.path.append('.')

def test_dual_subplot_function():
    """测试 save_dual_subplot_image 函数"""
    
    from Grouding_SAM_Main_UDA import save_dual_subplot_image
    
    print("测试 save_dual_subplot_image 函数...")
    
    # 创建测试图像
    h, w = 480, 640
    
    # 左图：带一些随机颜色块的图像（模拟mask annotation）
    left_image = np.random.randint(0, 255, (h, w, 3), dtype=np.uint8)
    # 添加一些彩色块模拟mask
    left_image[100:200, 100:200] = [255, 0, 0]  # 红色块
    left_image[300:400, 400:500] = [0, 255, 0]  # 绿色块
    
    # 右图：带散点的图像（模拟点云投影）
    right_image = np.random.randint(50, 200, (h, w, 3), dtype=np.uint8)
    
    # 在右图上添加一些点（模拟点云投影）
    # 创建一个matplotlib图来生成带散点的图像
    fig, ax = plt.subplots(figsize=(w/100, h/100), dpi=100)
    ax.imshow(right_image)
    
    # 添加随机散点
    n_points = 1000
    x_points = np.random.randint(0, w, n_points)
    y_points = np.random.randint(0, h, n_points)
    colors = np.random.rand(n_points, 3)
    
    ax.scatter(x_points, y_points, c=colors, s=2, alpha=0.7)
    ax.axis('off')
    ax.set_xlim(0, w)
    ax.set_ylim(h, 0)  # 翻转y轴
    
    # 将matplotlib图转换为numpy array
    fig.canvas.draw()
    buf = fig.canvas.tostring_rgb()
    ncols, nrows = fig.canvas.get_width_height()
    right_image_with_points = np.frombuffer(buf, dtype=np.uint8).reshape(nrows, ncols, 3)
    plt.close(fig)
    
    print(f"测试数据:")
    print(f"  left_image shape: {left_image.shape}")
    print(f"  right_image_with_points shape: {right_image_with_points.shape}")
    
    try:
        # 确保输出目录存在
        os.makedirs("debug_output", exist_ok=True)
        
        # 测试保存函数
        save_path = "debug_output/test_dual_subplot.png"
        save_dual_subplot_image(
            left_image, right_image_with_points,
            'Test Left Image (Simulated Masks)', 'Test Right Image (Simulated Point Cloud)',
            save_path
        )
        
        print(f"✅ 双subplot图像已保存到: {save_path}")
        
        # 验证文件是否存在
        if os.path.exists(save_path):
            print("✅ 文件保存成功！")
            return True
        else:
            print("❌ 文件保存失败！")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_different_sizes():
    """测试不同尺寸的图像"""
    
    from Grouding_SAM_Main_UDA import save_dual_subplot_image
    
    print("\n测试不同尺寸的图像...")
    
    # 创建不同尺寸的图像
    left_image = np.random.randint(0, 255, (300, 400, 3), dtype=np.uint8)
    right_image = np.random.randint(0, 255, (600, 800, 3), dtype=np.uint8)
    
    try:
        save_path = "debug_output/test_different_sizes.png"
        save_dual_subplot_image(
            left_image, right_image,
            'Small Image (300x400)', 'Large Image (600x800)',
            save_path
        )
        
        print(f"✅ 不同尺寸图像测试成功: {save_path}")
        return True
        
    except Exception as e:
        print(f"❌ 不同尺寸图像测试失败: {e}")
        return False

if __name__ == "__main__":
    success1 = test_dual_subplot_function()
    success2 = test_different_sizes()
    
    if success1 and success2:
        print("\n🎉 所有测试通过！双subplot功能正常工作。")
        print("请查看 debug_output/ 目录中的生成图像。")
    else:
        print("\n💥 部分测试失败，需要进一步调试。")
