# Configuration for Cog ⚙️
# Reference: https://github.com/replicate/cog/blob/main/docs/yaml.md

build:
  gpu: true
  cuda: "11.7"
  system_packages:
    - "libgl1-mesa-glx"
    - "libglib2.0-0"
  python_version: "3.10"
  python_packages:
    - "timm==0.9.2"
    - "transformers==4.30.2"
    - "fairscale==0.4.13"
    - "pycocoevalcap==1.2"
    - "torch==1.13.0"
    - "torchvision==0.14.0"
    - "Pillow==9.5.0"
    - "scipy==1.10.1"
    - "opencv-python==********"
    - "addict==2.4.0"
    - "yapf==0.40.0"
    - "supervision==0.10.0"
    - git+https://github.com/openai/CLIP.git
    - ipython

predict: "predict.py:Predictor"
