import os
import os.path as osp
import torch
import numpy as np
import pickle
from tqdm import tqdm
import h5py
import mmengine

from nuscenes.nuscenes import NuScenes
from tools.projection.pc2img import _LidarPointCloud
from pyquaternion import Quaternion
from datasets.transforms.PLRefine_add import PLRefineAdd

from tools.pkl_utils import load_point_mask_viewimages
from tools.projection.pc2img import get_lidar2img


SHIFT_TYPE = 'mini'
if SHIFT_TYPE == 'city':
    TARGET_FOLDER = 'SG'
elif SHIFT_TYPE == 'light':
    TARGET_FOLDER = 'night'
elif SHIFT_TYPE == 'weather':
    TARGET_FOLDER = 'rain'
elif SHIFT_TYPE == 'mini':
    TARGET_FOLDER = 'train_1'

# 设置路径
proj_path = os.getcwd()
if SHIFT_TYPE == 'mini':
    dataroot = osp.join(proj_path, 'data/nuscenes_mini')
    pklfile = osp.join(dataroot, 'nuscenes_infos_train_1.pkl')
else:
    dataroot = osp.join(proj_path, 'data/nuscenes_full')
    pklfile = osp.join(dataroot, 'pkl_files_UDA', f'nuscenes_infos_val_{TARGET_FOLDER}.pkl')

# 设置SAM和PL数据路径
sam_path = osp.join(dataroot, 'gsam_uda/pred_inst_pts_xcluster') 
# sam_path = osp.join(dataroot, 'sam_default/pred_inst_pts_v4_0906') 
# sam_path = osp.join(dataroot, 'htc/pred_inst_pts_no_cluster') 
# sam_path = osp.join(dataroot, 'htc/pred_inst_pts') 
# pl_path = osp.join(proj_path, f'output/UDA/PL_MT_{SHIFT_TYPE}/init')    # 需要替换为实际路径
pl_raw_path = osp.join(proj_path, f'output/UDA/PL_MT_{SHIFT_TYPE}/init_XMTdesign')
output_path = osp.join(proj_path, 'output/PLrefine')  # 需要替换为实际路径

# 确保输出目录存在
mmengine.mkdir_or_exist(output_path)

# 初始化nuscenes
nusc = NuScenes(version='v1.0-trainval', dataroot=dataroot, verbose=True)

# 读取info文件
with open(pklfile, 'rb') as f:
    data = pickle.load(f)

print(f"Total frames: {len(data['data_list'])}")


color_map = {
    0: [0, 0, 0],  # noise                 black
    1: [255, 120, 50],  # barrier               orange
    2: [255, 192, 203],  # bicycle               pink
    3: [255, 255, 0],  # bus                   yellow
    4: [0, 150, 245],  # car                   blue
    5: [0, 255, 255],  # construction_vehicle  cyan
    6: [255, 127, 0],  # motorcycle            dark orange
    7: [255, 0, 0],  # pedestrian            red
    8: [255, 240, 150],  # traffic_cone          light yellow
    9: [135, 60, 0],  # trailer               brown
    10: [160, 32, 240],  # truck                 purple
    11: [255, 0, 255],  # driveable_surface     dark pink
    12: [139, 137, 137],  # other_flat            dark red
    13: [75, 0, 75],  # sidewalk              dark purple
    14: [150, 240, 80],  # terrain               light green
    15: [230, 230, 250],  # manmade               white
    16: [0, 175, 0],  # vegetation            green
}   

label_map = {
    1: 0, 5: 0, 7: 0, 8: 0, 10: 0, 11: 0, 13: 0, 19: 0, 20: 0, 0: 0, 29: 0, 31: 0,
    9: 1, 14: 2, 15: 3, 16: 3, 17: 4, 18: 5, 21: 6, 2: 7, 3: 7, 4: 7, 6: 7, 12: 8,
    22: 9, 23: 10, 24: 11, 25: 12, 26: 13, 27: 14, 28: 15, 30: 16

}

VIEWS = ['CAM_FRONT', 'CAM_FRONT_RIGHT', 'CAM_FRONT_LEFT', 'CAM_BACK', 'CAM_BACK_LEFT', 'CAM_BACK_RIGHT']
data_prefix = {
    'dataset_path': 'data/nuscenes_mini',
    'lidar_path': 'data/nuscenes_mini/samples/LIDAR_TOP',
    'pts_panoptic_mask': 'data/nuscenes_mini/panoptic/v1.0-mini',
    'CAM_FRONT': 'data/nuscenes_mini/samples/CAM_FRONT',
    'CAM_FRONT_RIGHT': 'data/nuscenes_mini/samples/CAM_FRONT_RIGHT',
    'CAM_FRONT_LEFT': 'data/nuscenes_mini/samples/CAM_FRONT_LEFT',
    'CAM_BACK': 'data/nuscenes_mini/samples/CAM_BACK',
    'CAM_BACK_LEFT': 'data/nuscenes_mini/samples/CAM_BACK_LEFT',
    'CAM_BACK_RIGHT': 'data/nuscenes_mini/samples/CAM_BACK_RIGHT',}

label2cat = {'0': 'noise', '1': 'barrier', '2': 'bicycle', '3': 'bus', '4': 'car', 
             '5': 'construction_vehicle', '6': 'motorcycle', '7': 'pedestrian', 
             '8': 'traffic_cone', '9': 'trailer', '10': 'truck', '11': 'driveable_surface', 
             '12': 'other_flat', '13': 'sidewalk', '14': 'terrain', '15': 'manmade', '16': 'vegetation'}

point_cloud_range = [-50.0, -50.0, -5.0, 50.0, 50.0, 3.0]
point_cloud_range_polar = [0, -3.14159265359, -5., 50.0, 3.14159265359, 3.]

from typing import List, Dict, Optional
import numpy as np
from mmengine.logging import MMLogger, print_log

# Copyright (c) OpenMMLab. All rights reserved.
# Modified from mmdetection3d.
from typing import Dict, List, Tuple
PQReturnsType = Tuple[np.double, np.double, np.ndarray, np.ndarray, np.ndarray]

class EvalPanoptic:
    r"""Evaluate panoptic results for Semantickitti and NuScenes.
    Please refer to the `semantic kitti api
    <https://github.com/PRBonn/semantic-kitti-api/>`_ for more details

    Args:
        classes (list): Classes used in the dataset.
        thing_classes (list): Thing classes used in the dataset.
        stuff_classes (list): Stuff classes used in the dataset.
        include (list): Include classes in the dataset.
        dataset_type (str): Type of dataset.
        min_num_points (int): Minimum number of points of an object to be
            counted as ground truth in evaluation.
        id_offset (int): Offset for instance ids to concat with
            semantic labels.
        label2cat (dict[str]): Mapping from label to category.
        ignore_index (int): Indices of ignored classes in evaluation.
        logger (logging.Logger | str, optional): Logger used for printing.
            Defaults to None.
    """

    def __init__(self,
                 classes: List[str],
                 thing_classes: List[str],
                 stuff_classes: List[str],
                 include: List[int],
                 dataset_type: str,
                 min_num_points: int,
                 id_offset: int,
                 label2cat: Dict[str, str],
                 ignore_index: int,
                 logger: MMLogger = None):
        self.classes = classes
        self.thing_classes = thing_classes
        self.stuff_classes = stuff_classes
        self.include = include
        self.dataset_type = dataset_type
        self.ignore_index = ignore_index
        self.num_classes = len(classes)
        self.print_ignore_label=True
        if len(include) == self.num_classes:
            self.num_classes += 1
            self.print_ignore_label=False
        self.label2cat = label2cat
        self.logger = logger
        self.id_offset = id_offset
        self.eps = 1e-15
        self.min_num_points = min_num_points
        self.reset()

    def reset(self):
        """Reset class variables."""
        # general things
        # iou stuff
        self.confusion_matrix = np.zeros((self.num_classes, self.num_classes),
                                         dtype=int)
        # panoptic stuff
        self.pan_tp = np.zeros(self.num_classes, dtype=int)
        self.pan_iou = np.zeros(self.num_classes, dtype=np.double)
        self.pan_fp = np.zeros(self.num_classes, dtype=int)
        self.pan_fn = np.zeros(self.num_classes, dtype=int)

        self.evaluated_fnames = []

    def evaluate(self, gt_labels: List[Dict[str, np.ndarray]],
                 seg_preds: List[Dict[str, np.ndarray]]) -> Dict[str, float]:
        """Evaluate the predictions.

        Args:
            gt_labels (list[dict[np.ndarray]]): Ground Truth.
            seg_preds (list[dict[np.ndarray]]): Predictions.

        Returns:
            dict[float]: The computed metrics. The keys are the names of
            the metrics, and the values are corresponding results.
        """
        assert len(seg_preds) == len(gt_labels)
        for f in range(len(seg_preds)):
            gt_semantic_seg = gt_labels[f]['pts_semantic_mask'].astype(int)
            gt_instance_seg = gt_labels[f]['pts_instance_mask'].astype(int)
            pred_semantic_seg = seg_preds[f]['pts_semantic_mask'].astype(int)
            pred_instance_seg = seg_preds[f]['pts_instance_mask'].astype(int)

            valid_mask = gt_semantic_seg != self.ignore_index
            gt_semantic_seg = gt_semantic_seg[valid_mask]
            gt_instance_seg = gt_instance_seg[valid_mask]
            pred_semantic_seg = pred_semantic_seg[valid_mask]
            pred_instance_seg = pred_instance_seg[valid_mask]

            self.add_semantic_sample(pred_semantic_seg, gt_semantic_seg)
            self.add_panoptic_sample(pred_semantic_seg, gt_semantic_seg,
                                     pred_instance_seg, gt_instance_seg)

        result_dicts = self.print_results()

        return result_dicts

    def print_results(self) -> Dict[str, float]:
        """Print results.

        Returns:
            dict[float]: The computed metrics. The keys are the names of
            the metrics, and the values are corresponding results.
        """
        pq, sq, rq, all_pq, all_sq, all_rq = self.get_pq()
        miou, iou = self.get_iou()

        # now make a nice dictionary
        output_dict = {}

        # make python variables
        pq = pq.item()
        sq = sq.item()
        rq = rq.item()
        all_pq = all_pq.flatten().tolist()
        all_sq = all_sq.flatten().tolist()
        all_rq = all_rq.flatten().tolist()
        miou = miou.item()
        iou = iou.flatten().tolist()

        output_dict['all'] = {}
        output_dict['all']['pq'] = pq
        output_dict['all']['sq'] = sq
        output_dict['all']['rq'] = rq
        output_dict['all']['miou'] = miou
        for idx, (_pq, _sq, _rq,
                  _iou) in enumerate(zip(all_pq, all_sq, all_rq, iou)):
            class_str = self.classes[idx]
            output_dict[class_str] = {}
            output_dict[class_str]['pq'] = _pq
            output_dict[class_str]['sq'] = _sq
            output_dict[class_str]['rq'] = _rq
            output_dict[class_str]['miou'] = _iou

        pq_dagger = np.mean(
            [float(output_dict[c]['pq']) for c in self.thing_classes] +
            [float(output_dict[c]['miou']) for c in self.stuff_classes])

        pq_things = np.mean(
            [float(output_dict[c]['pq']) for c in self.thing_classes])
        rq_things = np.mean(
            [float(output_dict[c]['rq']) for c in self.thing_classes])
        sq_things = np.mean(
            [float(output_dict[c]['sq']) for c in self.thing_classes])

        pq_stuff = np.mean(
            [float(output_dict[c]['pq']) for c in self.stuff_classes])
        rq_stuff = np.mean(
            [float(output_dict[c]['rq']) for c in self.stuff_classes])
        sq_stuff = np.mean(
            [float(output_dict[c]['sq']) for c in self.stuff_classes])

        result_dicts = {}
        result_dicts['pq'] = float(pq)
        result_dicts['pq_dagger'] = float(pq_dagger)
        result_dicts['sq_mean'] = float(sq)
        result_dicts['rq_mean'] = float(rq)
        result_dicts['miou'] = float(miou)
        result_dicts['pq_stuff'] = float(pq_stuff)
        result_dicts['rq_stuff'] = float(rq_stuff)
        result_dicts['sq_stuff'] = float(sq_stuff)
        result_dicts['pq_things'] = float(pq_things)
        result_dicts['rq_things'] = float(rq_things)
        result_dicts['sq_things'] = float(sq_things)

        if self.logger is not None:
            print_log('|        |   IoU   |   PQ   |   RQ   |  SQ   |',
                      self.logger)
            for k, v in output_dict.items():
                print_log(
                    '|{}| {:.4f} | {:.4f} | {:.4f} | {:.4f} |'.format(
                        k.ljust(8)[-8:], v['miou'], v['pq'], v['rq'], v['sq']),
                    self.logger)
            print_log('True Positive: ', self.logger)
            print_log('\t|\t'.join([str(x) for x in self.pan_tp]), self.logger)
            print_log('False Positive: ')
            print_log('\t|\t'.join([str(x) for x in self.pan_fp]), self.logger)
            print_log('False Negative: ')
            print_log('\t|\t'.join([str(x) for x in self.pan_fn]), self.logger)

        else:
            print('|        |   IoU   |   PQ   |   RQ   |  SQ   |')
            for k, v in output_dict.items():
                print('|{}| {:.4f} | {:.4f} | {:.4f} | {:.4f} |'.format(
                    k.ljust(8)[-8:], v['miou'], v['pq'], v['rq'], v['sq']))
            print('True Positive: ')
            print('\t|\t'.join([str(x) for x in self.pan_tp]))
            print('False Positive: ')
            print('\t|\t'.join([str(x) for x in self.pan_fp]))
            print('False Negative: ')
            print('\t|\t'.join([str(x) for x in self.pan_fn]))

        return result_dicts

    def get_pq(self) -> PQReturnsType:
        """Get results of PQ metric.

        Returns:
            tuple(np.ndarray): PQ, SQ, RQ of each class and all class.
        """
        # get PQ and first calculate for all classes
        sq_all = self.pan_iou.astype(np.double) / np.maximum(
            self.pan_tp.astype(np.double), self.eps)
        rq_all = self.pan_tp.astype(np.double) / np.maximum(
            self.pan_tp.astype(np.double) + 0.5 * self.pan_fp.astype(np.double)
            + 0.5 * self.pan_fn.astype(np.double), self.eps)
        pq_all = sq_all * rq_all

        # then do the REAL mean (no ignored classes)
        sq = sq_all[self.include].mean()
        rq = rq_all[self.include].mean()
        pq = pq_all[self.include].mean()

        if not self.print_ignore_label:
            sq_all = sq_all[self.include]
            rq_all = rq_all[self.include]
            pq_all = pq_all[self.include]

        return (pq, sq, rq, pq_all, sq_all, rq_all)

    def get_iou(self) -> Tuple[np.double, np.ndarray]:
        """Get results of IOU metric.

        Returns:
            tuple(np.ndarray): iou of all class and each class.
        """
        tp, fp, fn = self.get_iou_stats()
        intersection = tp
        union = tp + fp + fn
        union = np.maximum(union, self.eps)
        iou = intersection.astype(np.double) / union.astype(np.double)
        iou_mean = (intersection[self.include].astype(np.double) /
                    union[self.include].astype(np.double)).mean()

        if not self.print_ignore_label:
            iou = iou[self.include]
        return iou_mean, iou

    def get_iou_stats(self) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """Get IOU statistics of TP, FP and FN.

        Returns:
            tuple(np.ndarray): TP, FP, FN of all class.
        """
        # copy to avoid modifying the real deal
        conf = self.confusion_matrix.copy().astype(np.double)
        # remove fp from confusion on the ignore classes predictions
        # points that were predicted of another class, but were ignore
        # (corresponds to zeroing the cols of those classes,
        # since the predictions go on the rows)

        # get the clean stats
        tp = conf.diagonal()
        fp = conf.sum(axis=1) - tp
        fn = conf.sum(axis=0) - tp
        return tp, fp, fn

    def add_semantic_sample(self, semantic_preds: np.ndarray,
                            gt_semantics: np.ndarray):
        """Add one batch of semantic predictions and ground truths.

        Args:
            semantic_preds (np.ndarray): Semantic predictions.
            gt_semantics (np.ndarray): Semantic ground truths.
        """
        idxs = np.stack([semantic_preds, gt_semantics], axis=0)
        # make confusion matrix (cols = gt, rows = pred)
        np.add.at(self.confusion_matrix, tuple(idxs), 1)

    def add_panoptic_sample(self, semantic_preds: np.ndarray,
                            gt_semantics: np.ndarray,
                            instance_preds: np.ndarray,
                            gt_instances: np.ndarray):
        """Add one sample of panoptic predictions and ground truths for
        evaluation.

        Args:
            semantic_preds (np.ndarray): Semantic predictions.
            gt_semantics (np.ndarray): Semantic ground truths.
            instance_preds (np.ndarray): Instance predictions.
            gt_instances (np.ndarray): Instance ground truths.
        """
        # avoid zero (ignored label)
        instance_preds = instance_preds + 1

        if self.dataset_type == 'nuscenes':
            gt_instances = gt_instances % self.id_offset + gt_semantics * self.id_offset
        elif self.dataset_type == 'semantickitti':
            gt_instances = gt_instances // self.id_offset * self.id_offset + gt_semantics
        gt_instances = gt_instances + 1

        # first step is to count intersections > 0.5 IoU
        # for each class (except the ignored ones)
        for cl in range(self.num_classes):
            # get a class mask
            pred_inst_in_cl_mask = semantic_preds == cl
            gt_inst_in_cl_mask = gt_semantics == cl

            # get instance points in class (makes outside stuff 0)
            pred_inst_in_cl = instance_preds * pred_inst_in_cl_mask.astype(int)
            gt_inst_in_cl = gt_instances * gt_inst_in_cl_mask.astype(int)

            # generate the areas for each unique instance prediction
            unique_pred, counts_pred = np.unique(
                pred_inst_in_cl[pred_inst_in_cl > 0], return_counts=True)
            id2idx_pred = {id: idx for idx, id in enumerate(unique_pred)}
            matched_pred = np.array([False] * unique_pred.shape[0])

            # generate the areas for each unique instance gt_np
            unique_gt, counts_gt = np.unique(
                gt_inst_in_cl[gt_inst_in_cl > 0], return_counts=True)
            id2idx_gt = {id: idx for idx, id in enumerate(unique_gt)}
            matched_gt = np.array([False] * unique_gt.shape[0])

            # generate intersection using offset
            valid_combos = np.logical_and(pred_inst_in_cl > 0,
                                          gt_inst_in_cl > 0)
            id_offset_combo = pred_inst_in_cl[
                valid_combos] + self.id_offset * gt_inst_in_cl[valid_combos]
            unique_combo, counts_combo = np.unique(
                id_offset_combo, return_counts=True)

            # generate an intersection map
            # count the intersections with over 0.5 IoU as TP
            gt_labels = unique_combo // self.id_offset
            pred_labels = unique_combo % self.id_offset
            gt_areas = np.array([counts_gt[id2idx_gt[id]] for id in gt_labels])
            pred_areas = np.array(
                [counts_pred[id2idx_pred[id]] for id in pred_labels])
            intersections = counts_combo
            unions = gt_areas + pred_areas - intersections
            ious = intersections.astype(float) / unions.astype(float)

            tp_indexes = ious > 0.5
            self.pan_tp[cl] += np.sum(tp_indexes)
            self.pan_iou[cl] += np.sum(ious[tp_indexes])

            matched_gt[[id2idx_gt[id] for id in gt_labels[tp_indexes]]] = True
            matched_pred[[id2idx_pred[id]
                          for id in pred_labels[tp_indexes]]] = True

            # count the FN
            if len(counts_gt) > 0:
                self.pan_fn[cl] += np.sum(
                    np.logical_and(counts_gt >= self.min_num_points,
                                   ~matched_gt))

            # count the FP
            if len(matched_pred) > 0:
                self.pan_fp[cl] += np.sum(
                    np.logical_and(counts_pred >= self.min_num_points,
                                   ~matched_pred))


def panoptic_seg_eval(gt_labels: List[np.ndarray],
                      seg_preds: List[np.ndarray],
                      classes: List[str],
                      thing_classes: List[str],
                      stuff_classes: List[str],
                      include: List[int],
                      dataset_type: str,
                      min_num_points: int,
                      id_offset: int,
                      label2cat: Dict[str, str],
                      ignore_index: int,
                      logger: MMLogger = None,
                      print_log: Optional[bool] = True) -> Dict[str, float]:
    """Panoptic Segmentation Evaluation.

    Evaluate the result of the panoptic segmentation.

    Args:
        gt_labels (list[dict[np.ndarray]]): Ground Truth.
        seg_preds (list[dict[np.ndarray]]): Predictions.
        classes (list[str]): Classes used in the dataset.
        thing_classes (list[str]): Thing classes used in the dataset.
        stuff_classes (list[str]): Stuff classes used in the dataset.
        include (list): Include classes in the dataset.
        dataset_type (str): Type of dataset.
        min_num_points (int): Minimum point number of object to be
            counted as ground truth in evaluation.
        id_offset (int): Offset for instance ids to concat with
            semantic labels.
        label2cat (dict[str]): Mapping from label to category.
        ignore_index (int): Indices of ignored classes in evaluation.
        logger (logging.Logger | str, optional): Logger used for printing.
            Defaults to None.

    Returns:
        dict[float]: Dict of results.
    """
    panoptic_seg_eval = EvalPanoptic(classes, thing_classes, stuff_classes, include, dataset_type,
                                     min_num_points, id_offset, label2cat,
                                     ignore_index, logger)
    ret_dict = panoptic_seg_eval.evaluate(gt_labels, seg_preds)
    return ret_dict

import re
import numpy as np
import torch
from mmcv.transforms import BaseTransform
from mmdet3d.registry import TRANSFORMS
from mmdet3d.datasets.transforms.transforms_3d import Compose
from mmdet3d.structures.points import BasePoints
from typing import List, Optional, Sequence

from tools.projection.pc2img import proj_lidar2img
from tools.projection.img_aug import merge_images_yaw, fit_box_cv, expand_box, crop_box_img, paste_box_img, merge_images_pitch_torch, fit_to_box, draw_dashed_box, merge_images_yaw_torch, divide_point_cloud_yaw, divide_point_cloud_pitch, split_yaw, select_mode_by_ratio, update_inst_img

@TRANSFORMS.register_module(force=True)
class PLRefineAdd_GT(BaseTransform):
    """
    PL refinement by adding instances from SAM.
    """
    def __init__(self,
                 img_aug: bool = True,
                 stuff_classes: Optional[List[int]] = None,
                 ignore_classes: Optional[List[int]] = None
                 ):
        self.img_aug = img_aug
        self.stuff_classes = stuff_classes
        self.ignore_classes = ignore_classes
        if isinstance(ignore_classes, int):
            self.ignore_classes = [ignore_classes]

    def transform(self, input_dict):

        points = input_dict['points']
        pl_psem = input_dict['pseudo_label']['pts_semantic_mask']
        pl_pins = input_dict['pseudo_label']['pts_instance_mask']
        pl_pscore = input_dict['pseudo_label']['pts_sem_score']
        sam_pmask = input_dict['sam']['sam_pmask']
        sam_pscore = input_dict['sam']['sam_pscore']
        
        # gt mask
        gt_psem = input_dict['pts_semantic_mask']
        gt_pins = input_dict['pts_instance_mask']

        assert points.shape[0] == pl_psem.shape[0] == sam_pmask.shape[0]
        assert isinstance(points, np.ndarray), "Points should be a numpy array."

        unique_classes = np.unique(pl_psem)
        refined_points = []
        refined_psem = []
        refined_pins = []
        refined_pscore = []
        refined_sam_pmask = []  # 新增：同步更新SAM mask
        refined_sam_pscore = []  # 新增：同步更新SAM score
        refined_gt_psem = []
        refined_gt_pins = []

        instance_dict = {}
        for inst_id in np.unique(pl_pins):
            mask = pl_pins == inst_id
            sem_label = np.unique(pl_psem[mask])
            # 对stuff class和thing class采用不同的处理逻辑
            if len(sem_label) == 1:
                # 单一语义标签的情况
                score = pl_pscore[mask].mean()
                instance_dict[inst_id] = {
                    'mask': mask,
                    'semantic': sem_label[0],
                    'score': score
                }
            else:
                # 多语义标签的情况，检查是否包含stuff class
                stuff_labels = [label for label in sem_label if label in self.stuff_classes]
                if stuff_labels:
                    # 如果包含stuff class，为每个stuff label创建一个mask
                    for stuff_label in stuff_labels:
                        stuff_mask = mask & (pl_psem == stuff_label)
                        if np.any(stuff_mask):  # 确保mask不为空
                            score = pl_pscore[stuff_mask].mean()
                            instance_dict[f"{inst_id}_{stuff_label}"] = {
                                'mask': stuff_mask,
                                'semantic': stuff_label,
                                'score': score
                            }
                # 如果不包含stuff class且有多个标签，则跳过
                else:
                    continue
                
        avg_score_per_class = {
            cls: pl_pscore[pl_psem == cls].mean() for cls in unique_classes
        }

        # pl_coord = points.copy()
        for inst_id, inst_info in instance_dict.items():
            sem = inst_info['semantic']
            score = inst_info['score']
            mask = inst_info['mask']

            # if sem > 10:
            #     print(sem)
            if score >= avg_score_per_class.get(sem, 1.0) or sem in self.stuff_classes or sem in self.ignore_classes:
                # print(sem)
                refined_points.append(points[mask])
                refined_psem.append(pl_psem[mask])
                refined_pins.append(pl_pins[mask])
                refined_pscore.append(pl_pscore[mask])
                # 同步更新SAM mask
                refined_sam_pmask.append(sam_pmask[mask])
                refined_sam_pscore.append(sam_pscore[mask])
                refined_gt_psem.append(gt_psem[mask])
                refined_gt_pins.append(gt_pins[mask])
                continue

            # #1. use iou to match SAM instances
            # inst_coords = pl_coord[mask]
            # best_iou, best_sam_id = 0, -1
            # for sam_id in np.unique(sam_pmask):
            #     sam_mask = sam_pmask == sam_id
            #     sam_coords = pl_coord[sam_mask]

            #     inter = np.sum(np.isin(inst_coords.view([('', inst_coords.dtype)]*3), sam_coords.view([('', sam_coords.dtype)]*3)))
            #     union = len(inst_coords) + len(sam_coords) - inter
            #     iou = inter / union if union != 0 else 0
            #     if iou > best_iou:
            #         best_iou, best_sam_id = iou, sam_id
            # if best_sam_id == -1 or best_iou < 0.1:
            #     continue  # 没找到合适匹配
            
            # #2. use dominant SAM instance in this area
            sam_ids_in_mask = sam_pmask[mask]
            if len(sam_ids_in_mask) == 0:
                continue
            values, counts = np.unique(sam_ids_in_mask, return_counts=True)
            best_sam_id = values[np.argmax(counts)]

            sam_mask = sam_pmask == best_sam_id
            refined_points.append(points[sam_mask])
            refined_psem.append(np.full(np.sum(sam_mask), sem, dtype=np.int64)) # use PL semantic label
            refined_pins.append(np.full(np.sum(sam_mask), inst_id, dtype=np.int64))
            refined_pscore.append(np.full(np.sum(sam_mask), sam_pscore[sam_mask][0], dtype=np.float32))
            refined_sam_pmask.append(sam_pmask[sam_mask])
            refined_sam_pscore.append(sam_pscore[sam_mask])
            refined_gt_psem.append(gt_psem[sam_mask])
            refined_gt_pins.append(gt_pins[sam_mask])

            if self.img_aug:
                img_list = input_dict['img']
                lidar2img = input_dict['img_metas']['lidar2img']
                ori_shape = input_dict['img_metas']['ori_shape']
                scale = input_dict['img_metas']['scale_factor'][0]

                sam_coord = points[sam_mask][:, :3]
                for i, img in enumerate(img_list):
                    proj_pts, vis_mask = proj_lidar2img(
                        torch.tensor(sam_coord),
                        lidar2img[i],
                        img_size=(ori_shape[1], ori_shape[0]),
                        min_dist=1.0)
                    proj_pts = (proj_pts * scale).numpy()
                    box = fit_box_cv(proj_pts)
                    box = expand_box(box, 10, img.shape[:2])
                    patch = crop_box_img(box, img)
                    input_dict['img'][i] = paste_box_img(box, img, patch)

        # 合并所有精炼后的数据
        dedup_input = deduplicate_points(
            np.concatenate(refined_points),
            {
                'pseudo_label': {
                    'pts_semantic_mask': np.concatenate(refined_psem),
                    'pts_instance_mask': np.concatenate(refined_pins),
                    'pts_sem_score': np.concatenate(refined_pscore),
                },
                'sam': {
                    'sam_pmask': np.concatenate(refined_sam_pmask),
                    'sam_pscore': np.concatenate(refined_sam_pscore)
                },
                'pts_semantic_mask': np.concatenate(refined_gt_psem),
                'pts_instance_mask': np.concatenate(refined_gt_pins)
            }
        )
        
        for key in input_dict:
            if key not in dedup_input:
                dedup_input[key] = input_dict[key]
            
        return dedup_input

def deduplicate_points(points, masks_dict, precision=1e-4):
    rounded = np.round(points[:, :3] / precision).astype(np.int64)
    keys = np.ascontiguousarray(rounded).view(
        np.dtype((np.void, rounded.dtype.itemsize * rounded.shape[1]))
    )
    _, unique_indices = np.unique(keys, return_index=True)
    unique_indices = np.sort(unique_indices)
    
    dedup = {
        'points': points[unique_indices]
    }
    for key, val in masks_dict.items():
        if isinstance(val, np.ndarray):
            dedup[key] = val[unique_indices]
        elif isinstance(val, dict):
            for sub_key, sub_val in val.items():
                dedup.setdefault(key, {})
                dedup[key][sub_key] = sub_val[unique_indices]
    return dedup

# vis
import numpy as np, matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D               # noqa: F401; needed for 3-D

# def plot_superpoints_matplotlib(xyz, labels=None, voxel_skip=1, fig_size=(6, 5)):
#     """
#     Quick 3-D scatter plot for super-points inside Jupyter Notebook.

#     Parameters
#     ----------
#     xyz : (N,3) array-like
#         Coordinates (metres).
#     labels : (N,) int array or None
#         Semantic / instance IDs.  None → all points同色.
#     voxel_skip : int, optional
#         Plot every k-th point for speed; 1 = no skip.
#     fig_size : tuple, optional
#         Figure size in inches.
#     """
#     # ── 数据预处理 ──
#     xyz_ = xyz[::voxel_skip]
#     if labels is not None:
#         labels_ = labels[::voxel_skip]
#         c_arg   = labels_
#     else:
#         c_arg = "blue"                                # 单色

#     # ── 绘图 ──
#     fig = plt.figure(figsize=fig_size)
#     ax  = fig.add_subplot(111, projection="3d")
#     ax.scatter(xyz_[:, 0], xyz_[:, 1], xyz_[:, 2],
#                c=c_arg, s=1, cmap="tab20", linewidths=0)

#     ax.set_xlabel("X (m)"); ax.set_ylabel("Y (m)"); ax.set_zlabel("Z (m)")
#     ax.set_box_aspect([1, 1, 0.5])                   # 等宽, 压扁一点方便看
    
#     # 离散色条（仅当有 labels 时）
#     if labels is not None:
#         import matplotlib.colors as mcolors
#         bounds = np.arange(labels_.min(), labels_.max()+2)
#         norm   = mcolors.BoundaryNorm(bounds, ncolors=plt.get_cmap("tab20").N)
#         mappable = plt.cm.ScalarMappable(norm=norm, cmap="tab20")
#         mappable.set_array([])
#         fig.colorbar(mappable, ticks=bounds, shrink=0.6, label="label id")

#     plt.show()

import numpy as np, matplotlib.pyplot as plt, matplotlib.colors as mcolors
from mpl_toolkits.mplot3d import Axes3D  # noqa: F401

def plot_superpoints_matplotlib(xyz, labels, voxel_skip=1, fig_size=(6,5)):
    # ↓ 只保留部分点加速渲染
    xyz_, labels_ = xyz[::voxel_skip], labels[::voxel_skip]

    n_cls  = labels_.max() - labels_.min() + 1          # 类别数
    cmap   = plt.get_cmap("tab20", n_cls)               # 强制离散成 n_cls 颜色
    bounds = np.arange(n_cls + 1)                       # 0,1,…,n_cls
    norm   = mcolors.BoundaryNorm(bounds, cmap.N)       # 颜色→整数区间

    fig = plt.figure(figsize=fig_size)
    ax  = fig.add_subplot(111, projection="3d")
    sc  = ax.scatter(xyz_[:,0], xyz_[:,1], xyz_[:,2],
                     c=labels_, cmap=cmap, norm=norm,
                     s=1, linewidths=0)

    fig.colorbar(sc, ticks=bounds, shrink=0.6, label="label id")
    ax.set_xlabel("X"); ax.set_ylabel("Y"); ax.set_zlabel("Z")
    ax.set_box_aspect([1,1,0.5])
    plt.show()

import numpy as np, matplotlib.pyplot as plt, matplotlib.colors as mcolors
from typing import Optional, Tuple

def plot_superpoints_bev_matplotlib(
    xyz: np.ndarray,
    labels: Optional[np.ndarray] = None,
    *,
    sample_step: int = 1,
    grid_size: Optional[float] = None,
    fig_size: Tuple[int, int] = (6, 6),
    cmap_name: str = "tab20",
    point_size: float = 2.0,
) -> None:
    """
    Bird-Eye-View 可视化 super-points / 点云（Matplotlib 版）。

    Parameters
    ----------
    xyz : (N, 3) array-like
        点坐标，单位任意（常见为米）。只使用前两维 (x, y)。
    labels : (N,) int array 或 None
        每点语义 / 实例 ID；None 表示单色点云。
    sample_step : int, 可选
        绘制每隔多少个点，以加速渲染。≥1；1 表示全部点。
    grid_size : float 或 None, 可选
        若给定，将把 (x, y) 落到网格中心（更像 BEV 像素）。单位同 xyz。
        例：0.2 表示 20 cm 分辨率；None 则直接散点。
    fig_size : (w, h) in inches
        Matplotlib figure 尺寸。
    cmap_name : str
        离散色卡名称；推荐 “tab20”, “tab20b”, “gist_ncar” 等。
    point_size : float
        散点直径 (`s` 参数)。
    """
    # ── 1 预处理 ──────────────────────────────────────────
    xyz_ = xyz[::sample_step, :2]          # 只取 (x, y)
    if labels is not None:
        labels_ = labels[::sample_step]
    else:
        labels_ = None

    # 网格量化（可选），可把点落到 BEV 像素中心
    if grid_size is not None and grid_size > 0:
        xyz_ = (np.floor(xyz_ / grid_size) + 0.5) * grid_size

    # ── 2 构建颜色映射 ──────────────────────────────────
    if labels_ is None:
        c_arg, cmap, norm = "k", None, None            # 单色黑点
    else:
        lbl_min = int(labels_.min())
        lbl_max = int(labels_.max())
        n_cls   = lbl_max - lbl_min + 1
        cmap    = plt.get_cmap(cmap_name, n_cls)       # 强制离散 n_cls 色
        bounds  = np.arange(lbl_min, lbl_max + 2)
        norm    = mcolors.BoundaryNorm(bounds, cmap.N) # 每个整数→唯一色
        c_arg   = labels_

    # ── 3 绘图 ──────────────────────────────────────────
    fig, ax = plt.subplots(figsize=fig_size)
    sc = ax.scatter(
        xyz_[:, 0],
        xyz_[:, 1],
        c=c_arg,
        cmap=cmap,
        norm=norm,
        s=point_size,
        linewidths=0,
        marker="s" if grid_size else "o",
    )

    ax.set_aspect("equal", adjustable="box")           # BEV 等比
    ax.set_xlabel("X")
    ax.set_ylabel("Y")
    ax.set_title("Super-point BEV")

    # origin 在左下 or 左上依项目约定，这里示例不翻转 Y
    # 若想向后为正、左为正，可 ax.invert_yaxis()

    if labels_ is not None:
        fig.colorbar(sc, ax=ax, shrink=1,
                     ticks=np.unique(labels_), label="label id")

    plt.show()

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.colors as mcolors
from mpl_toolkits.mplot3d import Axes3D      # noqa: F401 – 3-D 依赖

def visualize_semantic_pointcloud(
        xyz: np.ndarray,
        sem_labels: np.ndarray,
        color_map: dict,
        *,
        show_in_bev: bool = False,       # ← 新增参数
        point_size: float = 1.0,
        show_colorbar: bool = True,
        figsize=(7, 6),
    ) -> None:
    """
    用 color_map 可视化带语义标签的点云。
    若 show_in_bev=True，则渲染 X-Y 平面俯视图，否则为 3-D 散点。

    Parameters
    ----------
    xyz : (N,3) ndarray[float]
    sem_labels : (N,) ndarray[int]
    color_map : {label: [R,G,B]}
    show_in_bev : bool
        True → BEV；False → 3-D。
    point_size : float
    show_colorbar : bool
    figsize : tuple
    """
    # ---------- 1. 标签映射到 0–1 RGB ----------
    cmap_arr = np.zeros((len(color_map), 3))
    for k, v in color_map.items():
        cmap_arr[k] = np.array(v) / 255.0

    colors = cmap_arr[sem_labels]           # (N,3)

    # ---------- 2. 选择视图 ----------
    if show_in_bev:
        fig, ax = plt.subplots(figsize=figsize)
        sc = ax.scatter(
            xyz[:, 0], xyz[:, 1],           # 只用 X-Y
            c=colors, s=point_size, linewidths=0
        )
        ax.set_aspect('equal', adjustable='box')
        ax.set_xlabel('X'); ax.set_ylabel('Y')
        ax.set_title('Semantic Point Cloud – BEV')
    else:
        fig = plt.figure(figsize=figsize)
        ax  = fig.add_subplot(111, projection='3d')
        sc = ax.scatter(
            xyz[:, 0], xyz[:, 1], xyz[:, 2],
            c=colors, s=point_size, linewidths=0
        )
        ax.set_xlabel('X'); ax.set_ylabel('Y'); ax.set_zlabel('Z')
        ax.set_box_aspect([1, 1, 0.6])
        ax.set_title('Semantic Point Cloud')

    # ---------- 3. 离散色条（两种视图通用） ----------
    if show_colorbar:
        cmap  = mcolors.ListedColormap(cmap_arr)
        bounds = np.arange(len(color_map)+1)
        norm   = mcolors.BoundaryNorm(bounds, cmap.N)
        mappable = plt.cm.ScalarMappable(norm=norm, cmap=cmap)
        mappable.set_array([])
        fig.colorbar(mappable, ticks=list(color_map.keys()),
                     shrink=0.65, label='Semantic ID')

    plt.show()

import numpy as np, matplotlib.pyplot as plt, matplotlib.colors as mcolors
from mpl_toolkits.mplot3d import Axes3D          # noqa: F401

def visualize_ground_error_map(
        xyz: np.ndarray,
        gt_sem_labels: np.ndarray,
        pred_labels: np.ndarray,
        stuff_classes: list,
        *,
        show_in_bev: bool = False,
        point_size: float = 2.0,
        figsize=(7, 6)
    ) -> None:
    """
    Error-map 可视化：pred==-1 为 ground；GT 属于 stuff_classes 为真 ground。

    Parameters
    ----------
    xyz : (N,3) float array         点坐标
    gt_sem_labels : (N,) int        语义 GT
    pred_labels : (N,) int          模型输出；-1 表示 pred ground
    stuff_classes : list[int]       视为 GT ground 的类 id
    show_in_bev : bool              True→BEV；False→3-D
    point_size : float              散点大小
    figsize : (w,h)                 图幅尺寸
    """
    # ---------- 1. 生成掩码 ----------
    gt_ground   = np.isin(gt_sem_labels, stuff_classes)
    pred_ground = (pred_labels == -1)

    correct = (gt_ground == pred_ground)          # 灰
    fp_mask =  pred_ground & ~gt_ground           # 蓝
    fn_mask = ~pred_ground &  gt_ground           # 红

    # ---------- 2. 颜色数组 ----------
    colors = np.empty((xyz.shape[0], 3))
    colors[correct] = [0.6, 0.6, 0.6]             # grey
    colors[fp_mask] = [0.0, 0.4, 1.0]             # blue
    colors[fn_mask] = [1.0, 0.0, 0.0]             # red

    # ---------- 3. 绘制 ----------
    if show_in_bev:                               # Bird-Eye-View
        fig, ax = plt.subplots(figsize=figsize)
        ax.scatter(xyz[:, 0], xyz[:, 1],
                   c=colors, s=point_size, linewidths=0)
        ax.set_aspect('equal', 'box')
        ax.set_xlabel('X'); ax.set_ylabel('Y')
        ax.set_title('Ground-Detection Error Map (BEV)')
    else:                                         # 3-D
        fig = plt.figure(figsize=figsize)
        ax  = fig.add_subplot(111, projection='3d')
        ax.scatter(xyz[:, 0], xyz[:, 1], xyz[:, 2],
                   c=colors, s=point_size, linewidths=0)
        ax.set_box_aspect([1, 1, 0.6])
        ax.set_xlabel('X'); ax.set_ylabel('Y'); ax.set_zlabel('Z')
        ax.set_title('Ground-Detection Error Map (3-D)')

    # ---------- 4. 自定义色条 ----------
    cmap = mcolors.ListedColormap([[0.6,0.6,0.6],
                                   [0.0,0.4,1.0],
                                   [1.0,0.0,0.0]])
    bounds = [0,1,2,3]
    norm   = mcolors.BoundaryNorm(bounds, cmap.N)
    mappable = plt.cm.ScalarMappable(norm=norm, cmap=cmap)
    mappable.set_array([])
    cbar = fig.colorbar(mappable, ticks=[0.5,1.5,2.5], shrink=0.7)
    cbar.ax.set_yticklabels(['Correct','FP','FN'])
    plt.show()


import numpy as np
import open3d as o3d

def get_ground_mask(
        pts_xyz: np.ndarray,
        distance_threshold: float = 0.20,
        ransac_n: int = 3,
        num_iterations: int = 2000,
) -> np.ndarray:
    """
    使用 Open3D-RANSAC 拟合最大平面来提取地面点。

    Parameters
    ----------
    pts_xyz : (N, 3) or (N, ≥3) ndarray
        点云坐标。只会使用前 3 维的 (x, y, z)。
    distance_threshold : float, optional
        点到拟合平面的最大距离，超过则视为非地面。默认 0.20 m。
    ransac_n : int, optional
        每次 RANSAC 采样的点数。默认 3。
    num_iterations : int, optional
        RANSAC 迭代次数。默认 2000。

    Returns
    -------
    ground_mask : (N,) ndarray[bool]
        True 表示地面点，False 表示非地面点。
    """
    if pts_xyz.ndim != 2 or pts_xyz.shape[1] < 3:
        raise ValueError("pts_xyz must be an array of shape (N, 3) or (N, ≥3).")

    # 1. 构造 Open3D PointCloud
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(pts_xyz[:, :3])

    # 2. RANSAC 拟合地面平面
    plane_model, inliers = pcd.segment_plane(
        distance_threshold=distance_threshold,
        ransac_n=ransac_n,
        num_iterations=num_iterations,
    )

    # 3. 生成布尔 mask
    ground_mask = np.zeros(len(pts_xyz), dtype=bool)
    ground_mask[inliers] = True
    return ground_mask


data_slim = dict()
# every 10 frames
if SHIFT_TYPE == 'city':
    data_slim['data_list'] = data['data_list'][::50]
elif SHIFT_TYPE == 'light':
    data_slim['data_list'] = data['data_list'][::10]
frame = data_slim['data_list'][0]

res_list = load_point_mask_viewimages(data_slim, dataroot, point_dim=4)

from mmdet3d.structures.points import BasePoints, get_points_type

# coord_type = 'LIDAR'
# attribute_dims = None
# points_class = get_points_type(coord_type)

input_dicts = []
for i in range(len(res_list)):
    point = res_list[i]['lidar_point']
    # print(point.shape)
    pts_semantic_mask = res_list[i]['pts_semantic_mask']
    pts_instance_mask = res_list[i]['pts_instance_mask']
    image_list = list(res_list[i]['imgs_meta']['img'].values())
    image_list = [torch.tensor(img) for img in image_list]
    lidar2cams = res_list[i]['imgs_meta']['lidar2cam']
    cam2imgs = res_list[i]['imgs_meta']['cam2img']
    lidar2imgs = {}
    for i in range(len(lidar2cams)):
        view = VIEWS[i]
        lidar2img = get_lidar2img(torch.tensor(lidar2cams[view]).float(),  torch.tensor(cam2imgs[view]).float())
        lidar2imgs[view] = lidar2img

    input_dict_i = {
    # 'points': points_class(point, points_dim=point.shape[-1], attribute_dims=attribute_dims),
    'points': point,
    'images': image_list,
    'lidar2cam': lidar2cams,
    'cam2img': cam2imgs,
    'lidar2img': lidar2imgs,
    'bbox3d_fields': [],
    'pts_semantic_mask': pts_semantic_mask,
    'pts_instance_mask': pts_instance_mask,
    # 'transformation_3d_flow': [],
    }
    input_dicts.append(input_dict_i)

# sam 
# sam_path = osp.join(dataroot, 'sam_default/pred_inst_pts_v4_0906') 
sam_path = osp.join(dataroot, 'htc/pred_inst_pts_no_cluster') 

for i,frame in enumerate(res_list):
    # get filename
    sam_filename = os.path.basename(frame['lidar_path'])
    sam_mask = os.path.join(sam_path, sam_filename.replace('.pcd.bin', '.h5'))
    if not os.path.exists(sam_mask):
        print(f"sam_mask not found: {sam_mask}")
    with h5py.File(sam_mask, 'r') as f:
        sam_pmask_2d  = f['mask'][:]
        # sam_pscore_2d = f['score'][:]
        sam_pscore_2d = np.ones_like(sam_pmask_2d)

    input_dicts[i]['sam'] = {
                'sam_pmask': sam_pmask_2d,
                'sam_pscore': sam_pscore_2d
            }


from tools.general_tool import clamp_point_polar
from tools.projection.pc2img import cartesian2polar, polar2cartesian

def clamp_point_polar_np(point_coords, point_cloud_range):
    """
    Clamp point coordinates to the point cloud range.
    Params:
        point_coords: [N, 3 or 4]
        point_cloud_range: [min_radius, min_angle, min_height, max_radius, max_angle, max_height]
    Returns:
        res_coors: [N, 3 or 4]
    """
    # Convert to a tensor if the input is not already a tensor
    if not isinstance(point_coords, np.ndarray):
        raise ValueError("Input point_coords must be a tensor.")

    # Define min and max bounds
    min_bound = np.array(point_cloud_range[:3])
    max_bound = np.array(point_cloud_range[3:])

    # Clamp coordinates to within the point cloud range
    clamped_point_coords = np.clip(point_coords[:, :3], min_bound, max_bound)
    # print(clamped_point_coords.max(0))
    # print(clamped_point_coords.shape)
    # clamped_point_coords = np.concatenate((clamped_point_coords, point_coords[:, 3:]), axis=1)
    # print(clamped_point_coords.shape)
    return clamped_point_coords

# i = 0
start_idx = 7
end_idx = 9
for i in range(start_idx, end_idx):
    point_coor = input_dicts[i]['points'][:, :3]
    # clamp to 50x50 for better visualization
    point_clamp_polar = clamp_point_polar_np(cartesian2polar(point_coor), point_cloud_range_polar)
    point_clamp_cart = polar2cartesian(point_clamp_polar)
    ground_mask = get_ground_mask(point_coor)
    sam_pmask = input_dicts[i]['sam']['sam_pmask']
    
    plot_superpoints_bev_matplotlib(point_clamp_cart, sam_pmask)
    plot_superpoints_bev_matplotlib(point_clamp_cart, sam_pmask*~ground_mask)
    # print(f'num of unique sam_pmask: {len(np.unique(sam_pmask))-1}')

# PL
# pl_path = osp.join(proj_path, 'output/UDA/PL_MT_city/init')    # 需要替换为实际路径

for i,frame in enumerate(res_list):
    pl_data = dict()
    pl_filename = frame['sample_token']
    for n in ['pts_semantic_mask', 'pts_instance_mask', 'pts_sem_score']: # pts_mask_score
        d = np.load(os.path.join(pl_raw_path, n, pl_filename + '.npy'))
        pl_data[n] = d

    input_dicts[i]['pseudo_label'] = pl_data
    # print(np.unique(input_dicts[i]['pseudo_label']['pts_semantic_mask']))





# def process_single_frame(nusc, info, sam_path, pl_path):
#     """处理单帧数据
    
#     Args:
#         nusc: NuScenes实例
#         info: 当前帧信息
#         sam_path: SAM mask和score的保存路径
#         pl_path: PL数据的保存路径
#     """
#     # 1. 读取点云数据
#     lidar_path = info['lidar_path']
#     points = np.fromfile(lidar_path, dtype=np.float32)
#     points = points.reshape(-1, 5)  # nuscenes点云是5维的
    
#     # 2. 读取SAM mask和score
#     sam_file = osp.join(sam_path, f"{osp.splitext(osp.basename(lidar_path))[0]}.npz")
#     lidar_path = results['lidar_points']['lidar_path']
#     pred_query_path = lidar_path.replace('samples/LIDAR_TOP', self.cand_2d_folder).replace('.pcd.bin', '.h5')
#     if os.path.isfile(pred_query_path):
#         with h5py.File(pred_query_path, 'r') as f:
#             sam_pmask_2d  = f['mask'][:]
#             try:
#                 sam_pscore_2d = f['score'][:]
#     sam_data = np.load(sam_file)
#     sam_pmask = sam_data['mask']
#     sam_pscore = sam_data['score']
    
#     # 3. 读取PL数据
#     pl_file = osp.join(pl_path, f"{osp.splitext(osp.basename(lidar_path))[0]}.npz")
#     pl_data = np.load(pl_file)
#     pl_psem = pl_data['semantic_mask']
#     pl_pins = pl_data['instance_mask']
#     pl_pscore = pl_data['score']
    
#     # 4. 构建输入字典
#     input_dict = {
#         'points': points,
#         'pseudo_label': {
#             'pts_semantic_mask': pl_psem,
#             'pts_instance_mask': pl_pins,
#             'pts_sem_score': pl_pscore
#         },
#         'sam': {
#             'sam_pmask': sam_pmask,
#             'sam_pscore': sam_pscore
#         },
#         'img': None,  # 如果不需要img_aug可以设为None
#         'img_metas': None
#     }
    
#     # 5. 应用PLRefineAdd
#     plrefine = PLRefineAdd(
#         img_aug=False,  # 这里设置为False因为我们没有提供图像
#         stuff_classes=[0, 1],  # 根据实际情况设置
#         ignore_classes=[0]  # 根据实际情况设置
#     )
#     results = plrefine.transform(input_dict)
    
#     return results


## 3. 测试单帧处理


# 测试第一帧数据处理
test_info = data['infos'][0]
print(f"Processing frame: {test_info['lidar_path']}")

try:
    results = process_single_frame(nusc, test_info, sam_path, pl_path)
    print("\nResults:")
    print(f"Points shape: {results['points'].shape}")
    print(f"Semantic mask shape: {results['pseudo_label']['pts_semantic_mask'].shape}")
    print(f"Instance mask shape: {results['pseudo_label']['pts_instance_mask'].shape}")
    print(f"Score shape: {results['pseudo_label']['pts_sem_score'].shape}")
except Exception as e:
    print(f"Error processing frame: {str(e)}")


## 4. 处理所有帧数据


# 处理所有帧数据
for info in tqdm(data['infos'], desc='Processing frames'):
    try:
        results = process_single_frame(nusc, info, sam_path, pl_path)
        
        # 保存结果
        save_file = osp.join(output_path, f"{osp.splitext(osp.basename(info['lidar_path']))[0]}.npz")
        np.savez_compressed(
            save_file,
            points=results['points'],
            semantic_mask=results['pseudo_label']['pts_semantic_mask'],
            instance_mask=results['pseudo_label']['pts_instance_mask'],
            score=results['pseudo_label']['pts_sem_score']
        )
    except Exception as e:
        print(f"\nError processing frame {info['lidar_path']}: {str(e)}")
        continue


## 5. 验证结果


# 随机选择一个处理后的文件进行验证
import random

processed_files = [f for f in os.listdir(output_path) if f.endswith('.npz')]
if processed_files:
    test_file = random.choice(processed_files)
    test_data = np.load(osp.join(output_path, test_file))
    
    print(f"Checking file: {test_file}")
    print(f"\nData fields: {list(test_data.keys())}")
    print(f"Points shape: {test_data['points'].shape}")
    print(f"Semantic mask shape: {test_data['semantic_mask'].shape}")
    print(f"Instance mask shape: {test_data['instance_mask'].shape}")
    print(f"Score shape: {test_data['score'].shape}")
    
    # 验证数据一致性
    assert len(test_data['points']) == len(test_data['semantic_mask']) == \
           len(test_data['instance_mask']) == len(test_data['score']), \
           "数据长度不一致！"
    print("\n数据验证通过！")
else:
    print("没有找到处理后的文件！")


# V1: seed only provide position, sem label and instance mask are all from GT
def grow_by_gt_segments_v1(gt_sem: np.ndarray,
                         gt_inst: np.ndarray,
                         pl_sem: np.ndarray,
                         pl_score: np.ndarray,
                         ignore_label: int = -100):
    """Oracle upper‑bound growing using GT instances as superpoints.

    *Step 1*  动态阈值选择高置信度种子（每类取 > median&<=0.9）。
    *Step 2*  找到种子所在 GT instance id，整段扩张。
    *Step 3*  输出 grown_sem / grown_inst 作为性能上限参考。
    """
    # 1. 计算高置信度点掩码（per‑class median）
    high_conf_mask = np.zeros_like(pl_sem, dtype=bool)
    for cls in np.unique(pl_sem):
        if cls == ignore_label:
            continue
        cls_pts = pl_sem == cls
        if not np.any(cls_pts):
            continue
        median = np.median(pl_score[cls_pts])
        thresh = min(median, 0.9)
        high_conf_mask |= cls_pts & (pl_score > thresh)

    # 若无高置信度点，直接返回空 mask
    if not np.any(high_conf_mask):
        return np.zeros_like(gt_sem), np.zeros_like(gt_inst)

    # 2. 种子所在 GT instance id
    seed_gt_ids = np.unique(gt_inst[high_conf_mask])

    # 3. 扩张整段实例
    grown_sem = np.zeros_like(gt_sem)
    grown_inst = np.zeros_like(gt_inst)
    for inst_id in seed_gt_ids:
        seg_mask = gt_inst == inst_id
        cls_id = int(np.bincount(gt_sem[seg_mask]).argmax())
        grown_sem[seg_mask] = cls_id
        grown_inst[seg_mask] = inst_id

    return grown_sem, grown_inst

# V2: seed provides position and sem label, instance mask is from GT
def grow_by_gt_segments_v2(gt_sem: np.ndarray,
                         gt_inst: np.ndarray,
                         pl_sem: np.ndarray,
                         pl_score: np.ndarray,
                         ignore_label: int = -100):
    """Oracle upper‑bound growing using GT instances as superpoints.

    改进：
        * semantic label 由 *高置信度种子区* 的 \*预测结果\* 投票决定，
          而非直接使用 GT 语义。
        * 若某实例内没有高置信度种子，则跳过该实例（保持 ignore_label）。
    """
    # 1. 利用 per‑class median 确定高置信度点
    high_conf_mask = np.zeros_like(pl_sem, dtype=bool)
    for cls in np.unique(pl_sem):
        if cls in (ignore_label,):
            continue
        cls_pts = pl_sem == cls
        if not np.any(cls_pts):
            continue
        median = np.median(pl_score[cls_pts])
        thresh = min(median, 0.9)
        high_conf_mask |= cls_pts & (pl_score > thresh)

    if not np.any(high_conf_mask):
        return np.full_like(gt_sem, ignore_label), np.zeros_like(gt_inst)

    seed_gt_ids = np.unique(gt_inst[high_conf_mask])

    grown_sem = np.full_like(gt_sem, ignore_label)
    grown_inst = np.zeros_like(gt_inst)

    for inst_id in seed_gt_ids:
        seg_mask = gt_inst == inst_id
        seed_mask_inst = high_conf_mask & seg_mask
        if not np.any(seed_mask_inst):
            # 无高置信度预测，跳过该实例
            continue
        # 用高置信度区域的预测语义投票
        candidate_labels = pl_sem[seed_mask_inst]
        candidate_labels = candidate_labels[candidate_labels != ignore_label]
        if candidate_labels.size == 0:
            continue
        voted_cls = int(np.bincount(candidate_labels).argmax())
        grown_sem[seg_mask] = voted_cls
        grown_inst[seg_mask] = inst_id

    return grown_sem, grown_inst

# V3: seed provides position and instance mask, sem label is from GT
from tools.refine_pseudo_labels import refine_pseudo_labels
def grow_by_gt_segments_v3(gt_sem: np.ndarray,
                            gt_inst: np.ndarray, # not used
                            pl_inst: np.ndarray,
                            pl_sem: np.ndarray,
                            pl_score: np.ndarray = None,
                            ignore_label: int = -100):
    """Grow‑by‑GT (v3): **不修改 instance mask**，仅用 GT 语义修正每个预测实例的语义。

    Args:
        gt_sem (np.ndarray): ground‑truth semantic label per point.
        pl_inst (np.ndarray): predicted instance id per point (保持不变)。
        pl_sem (np.ndarray): predicted semantic label per point，将被修正。
        pl_score (np.ndarray, optional): confidence；若给定，可配合 `refine_pseudo_labels` 先过滤低置信度。
        ignore_label (int): label to ignore when无法投票。

    Returns:
        np.ndarray: refined semantic label (same shape as input),
                    instance id保持 `pl_inst` 原样。
    """
    refined_sem = pl_sem.copy()

    if pl_score is not None:
        refined_sem = refine_pseudo_labels(pl_score, refined_sem, ignore_label)

    for inst_id in np.unique(pl_inst):
        inst_mask = pl_inst == inst_id
        if not np.any(inst_mask):
            continue
        # 仅使用该预测实例内部点的 GT 语义做投票
        gt_labels_inside = gt_sem[inst_mask]
        # 排除 ignore_label 的 GT，但一般 GT 没有 ignore
        valid_gt = gt_labels_inside[gt_labels_inside != ignore_label]
        if valid_gt.size == 0:
            continue
        voted_cls = int(np.bincount(valid_gt).argmax())
        refined_sem[inst_mask] = voted_cls

    return refined_sem, pl_inst # pl_inst is not changed

raw_pred_path = osp.join(proj_path, f'output/UDA/PL_MT_{SHIFT_TYPE}/init_XMTdesign')

for i,frame in enumerate(res_list):
    pl_raw_data = dict()
    pl_filename = frame['sample_token']
    for n in ['pts_semantic_mask', 'pts_instance_mask', 'pts_sem_score']:
        d = np.load(os.path.join(raw_pred_path, n, pl_filename + '.npy'))
        pl_raw_data[n] = d

    # oracle_sem, oracle_inst = grow_by_gt_segments_v3(
    #     gt_sem=frame['pts_semantic_mask'],
    #     gt_inst=frame['pts_instance_mask'],
    #     pl_sem=pl_raw_data['pts_semantic_mask'],
    #     pl_inst=pl_raw_data['pts_instance_mask'],
    #     pl_score=pl_raw_data['pts_sem_score'],
    #     ignore_label=0
    # )
    # # re-label instance
    # _, oracle_inst = np.unique(
    #     oracle_inst, return_inverse=True)
    oracle_sem, oracle_inst = pl_raw_data['pts_semantic_mask'], pl_raw_data['pts_instance_mask']

    oracle_data = dict()
    oracle_data['pts_semantic_mask'] = oracle_sem
    oracle_data['pts_instance_mask'] = oracle_inst
    oracle_data['pts_sem_score'] = pl_raw_data['pts_sem_score']
    
    input_dicts[i]['oracle_pseudo_label'] = oracle_data

# PQ metrics
gt_labels, seg_preds = [], []
for i, frame in enumerate(input_dicts):
    eval_info = dict()
    eval_info['pts_instance_mask'] = frame['pts_instance_mask']    
    eval_info['pts_semantic_mask'] = frame['pts_semantic_mask']
    
    cpu_pred_3d = dict()
    cpu_pred_3d['pts_semantic_mask'] = frame['oracle_pseudo_label']['pts_semantic_mask']
    cpu_pred_3d['pts_instance_mask'] = frame['oracle_pseudo_label']['pts_instance_mask']

    gt_labels.append(eval_info) 
    seg_preds.append(cpu_pred_3d)
    # print(np.unique(frame['pseudo_label']['pts_semantic_mask']))
    
ret_dict = panoptic_seg_eval(gt_labels, seg_preds, classes_name,
            thing_classes, stuff_classes, include, dataset_type,
            min_num_points, id_offset,
            label2cat, ignore_index, logger)

import open3d as o3d, numpy as np, hdbscan, matplotlib.pyplot as plt, matplotlib as mpl

# label shape is not same as point shape, only for non-ground points
for i,frame in enumerate(input_dicts[:1]):
    point = frame['points']
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(point[:, :3])
    pcd = pcd.voxel_down_sample(0.05)
    
    # # ---------- 1 <USER> <GROUP> ----------
    plane_model, inliers = pcd.segment_plane(0.20, 3, 2000)
    ground     = pcd.select_by_index(inliers)
    nonground  = pcd.select_by_index(inliers, invert=True)
    
    # # ---------- 2 <USER> <GROUP>
    xyz = np.asarray(nonground.points)
    clusterer = hdbscan.HDBSCAN(min_cluster_size=40, min_samples=10, cluster_selection_method='eom')
    labels    = clusterer.fit_predict(xyz)
    
    # # ---------- 3 可视化 ----------
    # max_label = labels.max()
    # cmap = mpl.cm.get_cmap("tab20")
    # colors = cmap(labels / (max_label if max_label > 0 else 1))
    # colors[labels == -1] = (0, 0, 0, 1)
    # nonground.colors = o3d.utility.Vector3dVector(colors[:, :3])
    # ground.paint_uniform_color([0.5, 0.5, 0.5])

    # o3d.visualization.draw_geometries([ground, nonground])
    # np.save("frame_labels.npy", labels)  # 保存实例 ID

# label shape is the same as point shape, -1 for ground points
for i in range(len(res_list)):
    point = res_list[i]['pts_xyz']
    semantic_labels = res_list[i]['pts_semantic_mask']
    
    # 原始点 xyz (N,3)
    pcd_raw = o3d.geometry.PointCloud()
    pcd_raw.points = o3d.utility.Vector3dVector(point[:, :3])

    # 1. 拟合最大平面 (地面)
    plane_model, inliers = pcd_raw.segment_plane(distance_threshold=0.20,
                                                ransac_n=3,
                                                num_iterations=2000)   # :contentReference[oaicite:0]{index=0}
    # 2. 生成布尔掩码
    ground_mask    = np.zeros(len(point), dtype=bool)
    ground_mask[inliers] = True
    nonground_mask = ~ground_mask

    # 1. 带追踪的体素化
    pc_down, trace_indices, _ = pcd_raw.voxel_down_sample_and_trace(
            voxel_size=0.05,
            min_bound=pcd_raw.get_min_bound(),
            max_bound=pcd_raw.get_max_bound())                  # :contentReference[oaicite:2]{index=2}

    # 2. 聚类下采样后的非地面点
    non_ground_idx_down = [i for i, ids in enumerate(trace_indices) if not ground_mask[ids[0]]]
    xyz_down  = np.asarray(pc_down.points)[non_ground_idx_down]
    labels_dn = hdbscan.HDBSCAN(min_cluster_size=40,
                                min_samples=10).fit_predict(xyz_down)  # :contentReference[oaicite:3]{index=3}

    # 3. 回填到原始尺寸
    sp_ins_labels = np.full(len(point), -1, dtype=np.int32)
    for k, down_i in enumerate(non_ground_idx_down):
        sp_ins_labels[trace_indices[down_i]] = labels_dn[k]
    # for down_i, orig_ids in zip(non_ground_idx_down, trace_indices[non_ground_idx_down]):
        # sp_ins_labels[orig_ids] = labels_dn[down_i]

    assert ground_mask.shape == (len(point),)
    assert sp_ins_labels.shape == (len(point),)

    sp_ins_labels[ground_mask] = -1


import numpy as np, matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D               # noqa: F401; needed for 3-D

# def plot_superpoints_matplotlib(xyz, labels=None, voxel_skip=1, fig_size=(6, 5)):
#     """
#     Quick 3-D scatter plot for super-points inside Jupyter Notebook.

#     Parameters
#     ----------
#     xyz : (N,3) array-like
#         Coordinates (metres).
#     labels : (N,) int array or None
#         Semantic / instance IDs.  None → all points同色.
#     voxel_skip : int, optional
#         Plot every k-th point for speed; 1 = no skip.
#     fig_size : tuple, optional
#         Figure size in inches.
#     """
#     # ── 数据预处理 ──
#     xyz_ = xyz[::voxel_skip]
#     if labels is not None:
#         labels_ = labels[::voxel_skip]
#         c_arg   = labels_
#     else:
#         c_arg = "blue"                                # 单色

#     # ── 绘图 ──
#     fig = plt.figure(figsize=fig_size)
#     ax  = fig.add_subplot(111, projection="3d")
#     ax.scatter(xyz_[:, 0], xyz_[:, 1], xyz_[:, 2],
#                c=c_arg, s=1, cmap="tab20", linewidths=0)

#     ax.set_xlabel("X (m)"); ax.set_ylabel("Y (m)"); ax.set_zlabel("Z (m)")
#     ax.set_box_aspect([1, 1, 0.5])                   # 等宽, 压扁一点方便看
    
#     # 离散色条（仅当有 labels 时）
#     if labels is not None:
#         import matplotlib.colors as mcolors
#         bounds = np.arange(labels_.min(), labels_.max()+2)
#         norm   = mcolors.BoundaryNorm(bounds, ncolors=plt.get_cmap("tab20").N)
#         mappable = plt.cm.ScalarMappable(norm=norm, cmap="tab20")
#         mappable.set_array([])
#         fig.colorbar(mappable, ticks=bounds, shrink=0.6, label="label id")

#     plt.show()

import numpy as np, matplotlib.pyplot as plt, matplotlib.colors as mcolors
from mpl_toolkits.mplot3d import Axes3D  # noqa: F401

def plot_superpoints_matplotlib(xyz, labels, voxel_skip=1, fig_size=(6,5)):
    # ↓ 只保留部分点加速渲染
    xyz_, labels_ = xyz[::voxel_skip], labels[::voxel_skip]

    n_cls  = labels_.max() - labels_.min() + 1          # 类别数
    cmap   = plt.get_cmap("tab20", n_cls)               # 强制离散成 n_cls 颜色
    bounds = np.arange(n_cls + 1)                       # 0,1,…,n_cls
    norm   = mcolors.BoundaryNorm(bounds, cmap.N)       # 颜色→整数区间

    fig = plt.figure(figsize=fig_size)
    ax  = fig.add_subplot(111, projection="3d")
    sc  = ax.scatter(xyz_[:,0], xyz_[:,1], xyz_[:,2],
                     c=labels_, cmap=cmap, norm=norm,
                     s=1, linewidths=0)

    fig.colorbar(sc, ticks=bounds, shrink=0.6, label="label id")
    ax.set_xlabel("X"); ax.set_ylabel("Y"); ax.set_zlabel("Z")
    ax.set_box_aspect([1,1,0.5])
    plt.show()

import numpy as np, matplotlib.pyplot as plt, matplotlib.colors as mcolors
from typing import Optional, Tuple

def plot_superpoints_bev_matplotlib(
    xyz: np.ndarray,
    labels: Optional[np.ndarray] = None,
    *,
    sample_step: int = 1,
    grid_size: Optional[float] = None,
    fig_size: Tuple[int, int] = (6, 6),
    cmap_name: str = "tab20",
    point_size: float = 2.0,
) -> None:
    """
    Bird-Eye-View 可视化 super-points / 点云（Matplotlib 版）。

    Parameters
    ----------
    xyz : (N, 3) array-like
        点坐标，单位任意（常见为米）。只使用前两维 (x, y)。
    labels : (N,) int array 或 None
        每点语义 / 实例 ID；None 表示单色点云。
    sample_step : int, 可选
        绘制每隔多少个点，以加速渲染。≥1；1 表示全部点。
    grid_size : float 或 None, 可选
        若给定，将把 (x, y) 落到网格中心（更像 BEV 像素）。单位同 xyz。
        例：0.2 表示 20 cm 分辨率；None 则直接散点。
    fig_size : (w, h) in inches
        Matplotlib figure 尺寸。
    cmap_name : str
        离散色卡名称；推荐 “tab20”, “tab20b”, “gist_ncar” 等。
    point_size : float
        散点直径 (`s` 参数)。
    """
    # ── 1 预处理 ──────────────────────────────────────────
    xyz_ = xyz[::sample_step, :2]          # 只取 (x, y)
    if labels is not None:
        labels_ = labels[::sample_step]
    else:
        labels_ = None

    # 网格量化（可选），可把点落到 BEV 像素中心
    if grid_size is not None and grid_size > 0:
        xyz_ = (np.floor(xyz_ / grid_size) + 0.5) * grid_size

    # ── 2 构建颜色映射 ──────────────────────────────────
    if labels_ is None:
        c_arg, cmap, norm = "k", None, None            # 单色黑点
    else:
        lbl_min = int(labels_.min())
        lbl_max = int(labels_.max())
        n_cls   = lbl_max - lbl_min + 1
        cmap    = plt.get_cmap(cmap_name, n_cls)       # 强制离散 n_cls 色
        bounds  = np.arange(lbl_min, lbl_max + 2)
        norm    = mcolors.BoundaryNorm(bounds, cmap.N) # 每个整数→唯一色
        c_arg   = labels_

    # ── 3 绘图 ──────────────────────────────────────────
    fig, ax = plt.subplots(figsize=fig_size)
    sc = ax.scatter(
        xyz_[:, 0],
        xyz_[:, 1],
        c=c_arg,
        cmap=cmap,
        norm=norm,
        s=point_size,
        linewidths=0,
        marker="s" if grid_size else "o",
    )

    ax.set_aspect("equal", adjustable="box")           # BEV 等比
    ax.set_xlabel("X")
    ax.set_ylabel("Y")
    ax.set_title("Super-point BEV")

    # origin 在左下 or 左上依项目约定，这里示例不翻转 Y
    # 若想向后为正、左为正，可 ax.invert_yaxis()

    if labels_ is not None:
        fig.colorbar(sc, ax=ax, shrink=1,
                     ticks=np.unique(labels_), label="label id")

    plt.show()

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.colors as mcolors
from mpl_toolkits.mplot3d import Axes3D      # noqa: F401 – 3-D 依赖

def visualize_semantic_pointcloud(
        xyz: np.ndarray,
        sem_labels: np.ndarray,
        color_map: dict,
        *,
        show_in_bev: bool = False,       # ← 新增参数
        point_size: float = 1.0,
        show_colorbar: bool = True,
        figsize=(7, 6),
    ) -> None:
    """
    用 color_map 可视化带语义标签的点云。
    若 show_in_bev=True，则渲染 X-Y 平面俯视图，否则为 3-D 散点。

    Parameters
    ----------
    xyz : (N,3) ndarray[float]
    sem_labels : (N,) ndarray[int]
    color_map : {label: [R,G,B]}
    show_in_bev : bool
        True → BEV；False → 3-D。
    point_size : float
    show_colorbar : bool
    figsize : tuple
    """
    # ---------- 1. 标签映射到 0–1 RGB ----------
    cmap_arr = np.zeros((len(color_map), 3))
    for k, v in color_map.items():
        cmap_arr[k] = np.array(v) / 255.0

    colors = cmap_arr[sem_labels]           # (N,3)

    # ---------- 2. 选择视图 ----------
    if show_in_bev:
        fig, ax = plt.subplots(figsize=figsize)
        sc = ax.scatter(
            xyz[:, 0], xyz[:, 1],           # 只用 X-Y
            c=colors, s=point_size, linewidths=0
        )
        ax.set_aspect('equal', adjustable='box')
        ax.set_xlabel('X'); ax.set_ylabel('Y')
        ax.set_title('Semantic Point Cloud – BEV')
    else:
        fig = plt.figure(figsize=figsize)
        ax  = fig.add_subplot(111, projection='3d')
        sc = ax.scatter(
            xyz[:, 0], xyz[:, 1], xyz[:, 2],
            c=colors, s=point_size, linewidths=0
        )
        ax.set_xlabel('X'); ax.set_ylabel('Y'); ax.set_zlabel('Z')
        ax.set_box_aspect([1, 1, 0.6])
        ax.set_title('Semantic Point Cloud')

    # ---------- 3. 离散色条（两种视图通用） ----------
    if show_colorbar:
        cmap  = mcolors.ListedColormap(cmap_arr)
        bounds = np.arange(len(color_map)+1)
        norm   = mcolors.BoundaryNorm(bounds, cmap.N)
        mappable = plt.cm.ScalarMappable(norm=norm, cmap=cmap)
        mappable.set_array([])
        fig.colorbar(mappable, ticks=list(color_map.keys()),
                     shrink=0.65, label='Semantic ID')

    plt.show()

import numpy as np, matplotlib.pyplot as plt, matplotlib.colors as mcolors
from mpl_toolkits.mplot3d import Axes3D          # noqa: F401

def visualize_ground_error_map(
        xyz: np.ndarray,
        gt_sem_labels: np.ndarray,
        pred_labels: np.ndarray,
        stuff_classes: list,
        *,
        show_in_bev: bool = False,
        point_size: float = 2.0,
        figsize=(7, 6)
    ) -> None:
    """
    Error-map 可视化：pred==-1 为 ground；GT 属于 stuff_classes 为真 ground。

    Parameters
    ----------
    xyz : (N,3) float array         点坐标
    gt_sem_labels : (N,) int        语义 GT
    pred_labels : (N,) int          模型输出；-1 表示 pred ground
    stuff_classes : list[int]       视为 GT ground 的类 id
    show_in_bev : bool              True→BEV；False→3-D
    point_size : float              散点大小
    figsize : (w,h)                 图幅尺寸
    """
    # ---------- 1. 生成掩码 ----------
    gt_ground   = np.isin(gt_sem_labels, stuff_classes)
    pred_ground = (pred_labels == -1)

    correct = (gt_ground == pred_ground)          # 灰
    fp_mask =  pred_ground & ~gt_ground           # 蓝
    fn_mask = ~pred_ground &  gt_ground           # 红

    # ---------- 2. 颜色数组 ----------
    colors = np.empty((xyz.shape[0], 3))
    colors[correct] = [0.6, 0.6, 0.6]             # grey
    colors[fp_mask] = [0.0, 0.4, 1.0]             # blue
    colors[fn_mask] = [1.0, 0.0, 0.0]             # red

    # ---------- 3. 绘制 ----------
    if show_in_bev:                               # Bird-Eye-View
        fig, ax = plt.subplots(figsize=figsize)
        ax.scatter(xyz[:, 0], xyz[:, 1],
                   c=colors, s=point_size, linewidths=0)
        ax.set_aspect('equal', 'box')
        ax.set_xlabel('X'); ax.set_ylabel('Y')
        ax.set_title('Ground-Detection Error Map (BEV)')
    else:                                         # 3-D
        fig = plt.figure(figsize=figsize)
        ax  = fig.add_subplot(111, projection='3d')
        ax.scatter(xyz[:, 0], xyz[:, 1], xyz[:, 2],
                   c=colors, s=point_size, linewidths=0)
        ax.set_box_aspect([1, 1, 0.6])
        ax.set_xlabel('X'); ax.set_ylabel('Y'); ax.set_zlabel('Z')
        ax.set_title('Ground-Detection Error Map (3-D)')

    # ---------- 4. 自定义色条 ----------
    cmap = mcolors.ListedColormap([[0.6,0.6,0.6],
                                   [0.0,0.4,1.0],
                                   [1.0,0.0,0.0]])
    bounds = [0,1,2,3]
    norm   = mcolors.BoundaryNorm(bounds, cmap.N)
    mappable = plt.cm.ScalarMappable(norm=norm, cmap=cmap)
    mappable.set_array([])
    cbar = fig.colorbar(mappable, ticks=[0.5,1.5,2.5], shrink=0.7)
    cbar.ax.set_yticklabels(['Correct','FP','FN'])
    plt.show()


import numpy as np, matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D               # noqa: F401; needed for 3-D

# def plot_superpoints_matplotlib(xyz, labels=None, voxel_skip=1, fig_size=(6, 5)):
#     """
#     Quick 3-D scatter plot for super-points inside Jupyter Notebook.

#     Parameters
#     ----------
#     xyz : (N,3) array-like
#         Coordinates (metres).
#     labels : (N,) int array or None
#         Semantic / instance IDs.  None → all points同色.
#     voxel_skip : int, optional
#         Plot every k-th point for speed; 1 = no skip.
#     fig_size : tuple, optional
#         Figure size in inches.
#     """
#     # ── 数据预处理 ──
#     xyz_ = xyz[::voxel_skip]
#     if labels is not None:
#         labels_ = labels[::voxel_skip]
#         c_arg   = labels_
#     else:
#         c_arg = "blue"                                # 单色

#     # ── 绘图 ──
#     fig = plt.figure(figsize=fig_size)
#     ax  = fig.add_subplot(111, projection="3d")
#     ax.scatter(xyz_[:, 0], xyz_[:, 1], xyz_[:, 2],
#                c=c_arg, s=1, cmap="tab20", linewidths=0)

#     ax.set_xlabel("X (m)"); ax.set_ylabel("Y (m)"); ax.set_zlabel("Z (m)")
#     ax.set_box_aspect([1, 1, 0.5])                   # 等宽, 压扁一点方便看
    
#     # 离散色条（仅当有 labels 时）
#     if labels is not None:
#         import matplotlib.colors as mcolors
#         bounds = np.arange(labels_.min(), labels_.max()+2)
#         norm   = mcolors.BoundaryNorm(bounds, ncolors=plt.get_cmap("tab20").N)
#         mappable = plt.cm.ScalarMappable(norm=norm, cmap="tab20")
#         mappable.set_array([])
#         fig.colorbar(mappable, ticks=bounds, shrink=0.6, label="label id")

#     plt.show()

import numpy as np, matplotlib.pyplot as plt, matplotlib.colors as mcolors
from mpl_toolkits.mplot3d import Axes3D  # noqa: F401

def plot_superpoints_matplotlib(xyz, labels, voxel_skip=1, fig_size=(6,5)):
    # ↓ 只保留部分点加速渲染
    xyz_, labels_ = xyz[::voxel_skip], labels[::voxel_skip]

    n_cls  = labels_.max() - labels_.min() + 1          # 类别数
    cmap   = plt.get_cmap("tab20", n_cls)               # 强制离散成 n_cls 颜色
    bounds = np.arange(n_cls + 1)                       # 0,1,…,n_cls
    norm   = mcolors.BoundaryNorm(bounds, cmap.N)       # 颜色→整数区间

    fig = plt.figure(figsize=fig_size)
    ax  = fig.add_subplot(111, projection="3d")
    sc  = ax.scatter(xyz_[:,0], xyz_[:,1], xyz_[:,2],
                     c=labels_, cmap=cmap, norm=norm,
                     s=1, linewidths=0)

    fig.colorbar(sc, ticks=bounds, shrink=0.6, label="label id")
    ax.set_xlabel("X"); ax.set_ylabel("Y"); ax.set_zlabel("Z")
    ax.set_box_aspect([1,1,0.5])
    plt.show()

import numpy as np, matplotlib.pyplot as plt, matplotlib.colors as mcolors
from typing import Optional, Tuple

def plot_superpoints_bev_matplotlib(
    xyz: np.ndarray,
    labels: Optional[np.ndarray] = None,
    *,
    sample_step: int = 1,
    grid_size: Optional[float] = None,
    fig_size: Tuple[int, int] = (20, 20),
    cmap_name: str = "tab20",
    point_size: float = 2.0,
) -> None:
    """
    Bird-Eye-View 可视化 super-points / 点云（Matplotlib 版）。

    Parameters
    ----------
    xyz : (N, 3) array-like
        点坐标，单位任意（常见为米）。只使用前两维 (x, y)。
    labels : (N,) int array 或 None
        每点语义 / 实例 ID；None 表示单色点云。
    sample_step : int, 可选
        绘制每隔多少个点，以加速渲染。≥1；1 表示全部点。
    grid_size : float 或 None, 可选
        若给定，将把 (x, y) 落到网格中心（更像 BEV 像素）。单位同 xyz。
        例：0.2 表示 20 cm 分辨率；None 则直接散点。
    fig_size : (w, h) in inches
        Matplotlib figure 尺寸。
    cmap_name : str
        离散色卡名称；推荐 “tab20”, “tab20b”, “gist_ncar” 等。
    point_size : float
        散点直径 (`s` 参数)。
    """
    # ── 1 预处理 ──────────────────────────────────────────
    xyz_ = xyz[::sample_step, :2]          # 只取 (x, y)
    if labels is not None:
        labels_ = labels[::sample_step]
    else:
        labels_ = None

    # 网格量化（可选），可把点落到 BEV 像素中心
    if grid_size is not None and grid_size > 0:
        xyz_ = (np.floor(xyz_ / grid_size) + 0.5) * grid_size

    # ── 2 构建颜色映射 ──────────────────────────────────
    if labels_ is None:
        c_arg, cmap, norm = "k", None, None            # 单色黑点
    else:
        lbl_min = int(labels_.min())
        lbl_max = int(labels_.max())
        n_cls   = lbl_max - lbl_min + 1
        cmap    = plt.get_cmap(cmap_name, n_cls)       # 强制离散 n_cls 色
        bounds  = np.arange(lbl_min, lbl_max + 2)
        norm    = mcolors.BoundaryNorm(bounds, cmap.N) # 每个整数→唯一色
        c_arg   = labels_

    # ── 3 绘图 ──────────────────────────────────────────
    fig, ax = plt.subplots(figsize=fig_size)
    sc = ax.scatter(
        xyz_[:, 0],
        xyz_[:, 1],
        c=c_arg,
        cmap=cmap,
        norm=norm,
        s=point_size,
        linewidths=0,
        marker="s" if grid_size else "o",
    )

    ax.set_aspect("equal", adjustable="box")           # BEV 等比
    ax.set_xlabel("X")
    ax.set_ylabel("Y")
    ax.set_title("Super-point BEV")

    # origin 在左下 or 左上依项目约定，这里示例不翻转 Y
    # 若想向后为正、左为正，可 ax.invert_yaxis()

    if labels_ is not None:
        fig.colorbar(sc, ax=ax, shrink=1,
                     ticks=np.unique(labels_), label="label id")

    plt.show()

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.colors as mcolors
from mpl_toolkits.mplot3d import Axes3D      # noqa: F401 – 3-D 依赖

def visualize_semantic_pointcloud(
        xyz: np.ndarray,
        sem_labels: np.ndarray,
        color_map: dict,
        *,
        show_in_bev: bool = False,       # ← 新增参数
        point_size: float = 1.0,
        show_colorbar: bool = True,
        figsize=(7, 6),
    ) -> None:
    """
    用 color_map 可视化带语义标签的点云。
    若 show_in_bev=True，则渲染 X-Y 平面俯视图，否则为 3-D 散点。

    Parameters
    ----------
    xyz : (N,3) ndarray[float]
    sem_labels : (N,) ndarray[int]
    color_map : {label: [R,G,B]}
    show_in_bev : bool
        True → BEV；False → 3-D。
    point_size : float
    show_colorbar : bool
    figsize : tuple
    """
    # ---------- 1. 标签映射到 0–1 RGB ----------
    cmap_arr = np.zeros((len(color_map), 3))
    for k, v in color_map.items():
        cmap_arr[k] = np.array(v) / 255.0

    colors = cmap_arr[sem_labels]           # (N,3)

    # ---------- 2. 选择视图 ----------
    if show_in_bev:
        fig, ax = plt.subplots(figsize=figsize)
        sc = ax.scatter(
            xyz[:, 0], xyz[:, 1],           # 只用 X-Y
            c=colors, s=point_size, linewidths=0
        )
        ax.set_aspect('equal', adjustable='box')
        ax.set_xlabel('X'); ax.set_ylabel('Y')
        ax.set_title('Semantic Point Cloud – BEV')
    else:
        fig = plt.figure(figsize=figsize)
        ax  = fig.add_subplot(111, projection='3d')
        sc = ax.scatter(
            xyz[:, 0], xyz[:, 1], xyz[:, 2],
            c=colors, s=point_size, linewidths=0
        )
        ax.set_xlabel('X'); ax.set_ylabel('Y'); ax.set_zlabel('Z')
        ax.set_box_aspect([1, 1, 0.6])
        ax.set_title('Semantic Point Cloud')

    # ---------- 3. 离散色条（两种视图通用） ----------
    if show_colorbar:
        cmap  = mcolors.ListedColormap(cmap_arr)
        bounds = np.arange(len(color_map)+1)
        norm   = mcolors.BoundaryNorm(bounds, cmap.N)
        mappable = plt.cm.ScalarMappable(norm=norm, cmap=cmap)
        mappable.set_array([])
        fig.colorbar(mappable, ticks=list(color_map.keys()),
                     shrink=0.65, label='Semantic ID')

    plt.show()

import numpy as np, matplotlib.pyplot as plt, matplotlib.colors as mcolors
from mpl_toolkits.mplot3d import Axes3D          # noqa: F401

def visualize_ground_error_map(
        xyz: np.ndarray,
        gt_sem_labels: np.ndarray,
        pred_labels: np.ndarray,
        stuff_classes: list,
        *,
        show_in_bev: bool = False,
        point_size: float = 2.0,
        figsize=(7, 6)
    ) -> None:
    """
    Error-map 可视化：pred==-1 为 ground；GT 属于 stuff_classes 为真 ground。

    Parameters
    ----------
    xyz : (N,3) float array         点坐标
    gt_sem_labels : (N,) int        语义 GT
    pred_labels : (N,) int          模型输出；-1 表示 pred ground
    stuff_classes : list[int]       视为 GT ground 的类 id
    show_in_bev : bool              True→BEV；False→3-D
    point_size : float              散点大小
    figsize : (w,h)                 图幅尺寸
    """
    # ---------- 1. 生成掩码 ----------
    gt_ground   = np.isin(gt_sem_labels, stuff_classes)
    pred_ground = (pred_labels == -1)

    correct = (gt_ground == pred_ground)          # 灰
    fp_mask =  pred_ground & ~gt_ground           # 蓝
    fn_mask = ~pred_ground &  gt_ground           # 红

    # ---------- 2. 颜色数组 ----------
    colors = np.empty((xyz.shape[0], 3))
    colors[correct] = [0.6, 0.6, 0.6]             # grey
    colors[fp_mask] = [0.0, 0.4, 1.0]             # blue
    colors[fn_mask] = [1.0, 0.0, 0.0]             # red

    # ---------- 3. 绘制 ----------
    if show_in_bev:                               # Bird-Eye-View
        fig, ax = plt.subplots(figsize=figsize)
        ax.scatter(xyz[:, 0], xyz[:, 1],
                   c=colors, s=point_size, linewidths=0)
        ax.set_aspect('equal', 'box')
        ax.set_xlabel('X'); ax.set_ylabel('Y')
        ax.set_title('Ground-Detection Error Map (BEV)')
    else:                                         # 3-D
        fig = plt.figure(figsize=figsize)
        ax  = fig.add_subplot(111, projection='3d')
        ax.scatter(xyz[:, 0], xyz[:, 1], xyz[:, 2],
                   c=colors, s=point_size, linewidths=0)
        ax.set_box_aspect([1, 1, 0.6])
        ax.set_xlabel('X'); ax.set_ylabel('Y'); ax.set_zlabel('Z')
        ax.set_title('Ground-Detection Error Map (3-D)')

    # ---------- 4. 自定义色条 ----------
    cmap = mcolors.ListedColormap([[0.6,0.6,0.6],
                                   [0.0,0.4,1.0],
                                   [1.0,0.0,0.0]])
    bounds = [0,1,2,3]
    norm   = mcolors.BoundaryNorm(bounds, cmap.N)
    mappable = plt.cm.ScalarMappable(norm=norm, cmap=cmap)
    mappable.set_array([])
    cbar = fig.colorbar(mappable, ticks=[0.5,1.5,2.5], shrink=0.7)
    cbar.ax.set_yticklabels(['Correct','FP','FN'])
    plt.show()


# V3: split ground class
for i in range(len(res_list)):
    point = res_list[i]['pts_xyz']
    semantic_labels = res_list[i]['pts_semantic_mask']
    
    # 原始点 xyz (N,3)
    pcd_raw = o3d.geometry.PointCloud()
    pcd_raw.points = o3d.utility.Vector3dVector(point[:, :3])

    # 1. 拟合最大平面 (地面)
    plane_model, inliers = pcd_raw.segment_plane(distance_threshold=0.20,
                                                ransac_n=3,
                                                num_iterations=2000)   # :contentReference[oaicite:0]{index=0}
    # 2. 生成布尔掩码
    ground_mask    = np.zeros(len(point), dtype=bool)
    ground_mask[inliers] = True
    nonground_mask = ~ground_mask

    # 1. 带追踪的体素化
    pc_down, trace_indices, _ = pcd_raw.voxel_down_sample_and_trace(
            voxel_size=0.05,
            min_bound=pcd_raw.get_min_bound(),
            max_bound=pcd_raw.get_max_bound())                  # :contentReference[oaicite:2]{index=2}

    # 2. 聚类下采样后的非地面点
    non_ground_idx_down = [i for i, ids in enumerate(trace_indices) if not ground_mask[ids[0]]]
    xyz_down  = np.asarray(pc_down.points)[non_ground_idx_down]
    labels_dn = hdbscan.HDBSCAN(min_cluster_size=40,
                                min_samples=10).fit_predict(xyz_down)  # :contentReference[oaicite:3]{index=3}

    # 3. 回填到原始尺寸
    sp_ins_labels = np.full(len(point), -1, dtype=np.int32)
    for k, down_i in enumerate(non_ground_idx_down):
        sp_ins_labels[trace_indices[down_i]] = labels_dn[k]
    # for down_i, orig_ids in zip(non_ground_idx_down, trace_indices[non_ground_idx_down]):
        # sp_ins_labels[orig_ids] = labels_dn[down_i]

    assert ground_mask.shape == (len(point),)
    assert sp_ins_labels.shape == (len(point),)

    sp_ins_labels[ground_mask] = -1


# --- imports ---------------------------------------------------------------
import numpy as np
import open3d as o3d
import hdbscan

# ---------------------------------------------------------------------------


def tune_hdbscan_params(num_pts: int, is_ground: bool):
    """
    依据点数与地面/非地面属性动态设定 HDBSCAN 超参.
    Heuristic:   非地面 -> 小簇阈值; 地面 -> 大簇阈值.
    """
    if is_ground:
        # 地面点最多, 取 2% 点数但不低于 120 作为 min_cluster_size
        min_cluster_size = max(120, int(num_pts * 0.02))
        min_samples      = max(25,  int(num_pts * 0.003))
    else:
        # 物体实例: 0.5% 点数, 不低于 40
        min_cluster_size = max(40, int(num_pts * 0.005))
        min_samples      = max(8,  int(num_pts * 0.001))
    return min_cluster_size, min_samples


def process_RANSAC_HDB(points_xyz: np.ndarray,
                  voxel_size: float = 0.05,
                  dist_thresh: float = 0.20,
                  ground_mode: str = "single"):
    """
    Parameters
    ----------
    points_xyz : (N,3) float32/64
    voxel_size : float
        体素边长 (m).
    dist_thresh : float
        RANSAC 平面阈值 (m).
    ground_mode : {"single", "cluster"}
        "single"  -> 所有 ground 设置为 -1;
        "cluster" -> 地面内部再跑一次 HDBSCAN, 生成细分 id.

    Returns
    -------
    instance_labels : (N,) int32
        -1 表 ground; 其他非负整数为实例 id.
    """

    # ===== 0.  原始点云对象 =================================================
    pcd_raw = o3d.geometry.PointCloud()
    pcd_raw.points = o3d.utility.Vector3dVector(points_xyz)

    # ===== 1.  RANSAC 地面检测 =============================================
    _, inliers = pcd_raw.segment_plane(distance_threshold=dist_thresh,
                                       ransac_n=3,
                                       num_iterations=2000)
    ground_mask = np.zeros(len(points_xyz), dtype=bool)
    ground_mask[inliers] = True

    # ===== 2.  体素下采样 + trace =========================================
    pc_down, trace_indices, _ = pcd_raw.voxel_down_sample_and_trace(
        voxel_size=voxel_size,
        min_bound=pcd_raw.get_min_bound(),
        max_bound=pcd_raw.get_max_bound())
    trace_indices = np.asarray(trace_indices, dtype=object)
    xyz_down = np.asarray(pc_down.points)

    # 根据每个体素选一个原始点来判 ground / non-ground 
    is_down_ground = np.array([ground_mask[ids[0]] for ids in trace_indices])
    non_ground_idx = np.where(~is_down_ground)[0]
    ground_idx     = np.where(is_down_ground)[0]

    # ===== 3-A.  非地面聚类 -----------------------------------------------
    lbl_down = np.full(len(xyz_down), -1, dtype=np.int32)

    if non_ground_idx.size:
        mcs, ms = tune_hdbscan_params(non_ground_idx.size, is_ground=False)
        lbl_ng = hdbscan.HDBSCAN(min_cluster_size=mcs,
                                 min_samples=ms).fit_predict(xyz_down[non_ground_idx])
        # print(f"Non-ground clustering: {np.unique(lbl_ng, return_counts=True)}")
        lbl_down[non_ground_idx] = lbl_ng

    # ===== 3-B.  地面聚类 / 统一设 -1 --------------------------------------
    if ground_idx.size:
        if ground_mode == "cluster":
            mcs_g, ms_g = tune_hdbscan_params(ground_idx.size, is_ground=True)
            print(f"Ground clustering: mcs_g, ms_g")
            lbl_g = hdbscan.HDBSCAN(min_cluster_size=mcs_g,
                                    min_samples=ms_g).fit_predict(xyz_down[ground_idx])
            # 避免 id 混冲: 全局最大 id + 1 偏移
            offset = lbl_down.max() + 1
            lbl_g  = np.where(lbl_g == -1, -1, lbl_g + offset)
            lbl_down[ground_idx] = lbl_g     # -1 或细分 id
        else:
            lbl_down[ground_idx] = -1         # 整体 ground

    # ===== 4.  回填到原始大小 ============================================
    instance_labels = np.full(len(points_xyz), -1, dtype=np.int32)
    for down_i, orig_ids in enumerate(trace_indices):
        valid_ids = orig_ids[orig_ids >= 0]
        if valid_ids.size:
            # print(valid_ids, down_i)
            valid_ids = valid_ids.astype(np.int64)   # 或 np.intp / np.int32 均可
            instance_labels[valid_ids] = lbl_down[down_i]
        # instance_labels[orig_ids] = lbl_down[down_i]

    return instance_labels.astype(np.int32), ground_mask


# ================================  Example  ================================
# 假设 res_list 每帧包含:
#   'pts_xyz'           -> (N,3)
#   'pts_semantic_mask' -> (N,)  (这里只作为示例, 算法不再使用 GT)

for frm in input_dicts[:1]:
    xyz = frm['points'][:, :3]
    frm['sp_ins_labels'], frm['sp_ground_mask'] = process_RANSAC_HDB(
        xyz,
        voxel_size=0.05,
        dist_thresh=0.20,
        ground_mode=None      # None: 不细分; 改成 "cluster" 可细分 driveable/sidewalk
    )
    # 检查尺寸
    assert frm['sp_ins_labels'].shape == (len(xyz),)
    print(len(np.unique(frm['sp_ins_labels'])), np.unique(frm['sp_ins_labels']))


# plot_superpoints_matplotlib(xyz, labels)
# plot_superpoints_bev_matplotlib(xyz, labels)
semantic_labels = res_list[0]['pts_semantic_mask']
GT_ground = np.isin(semantic_labels, stuff_classes_num)
plot_superpoints_bev_matplotlib(point, ground_mask) # instance_labels)

# vis GT semantic 
semantic_labels = input_dicts[0]['pts_semantic_mask']
visualize_semantic_pointcloud(point, semantic_labels, color_map, show_in_bev=True)

# vis GT ground mask
# GT_ground = np.isin(semantic_labels, stuff_classes_num)
# plot_superpoints_bev_matplotlib(point, ground_mask) # instance_labels)

# error map for ground split
# stuff_classes_num = [11, 13, 14] # ground classes
# stuff_classes_num = range(11, 17) 
# visualize_ground_error_map(point, semantic_labels, instance_labels, stuff_classes=stuff_classes_num, show_in_bev=True)

# plot_superpoints_bev_matplotlib(point, frm['sp_ground_mask']*frm['sp_ins_labels'])

ground_sp = frm['sp_ground_mask']*frm['sp_ins_labels']
plot_superpoints_bev_matplotlib(point[ground_sp!=0], ground_sp[ground_sp!=0])

# save superpoints ply
# save_superpoints_ply(xyz, labels, 'output/superpoint/test.ply')


import numpy as np
import open3d as o3d
from pathlib import Path
from typing import Union

def save_superpoints_ply(
    xyz: np.ndarray,
    labels: np.ndarray,
    filename: Union[str, Path],
    binary: bool = True,
    fallback_to_rgb: bool = True,
) -> None:
    """
    Save super-point results as a .ply that keeps an integer 'labels' field.

    Parameters
    ----------
    xyz : (N, 3) float32 / float64
        Point coordinates. Unit is whatever your array uses (usually metres).
    labels : (N,) int
        Semantic or instance ID per point.
    filename : str or Path
        Destination .ply file path.
    binary : bool, default=True
        Write in binary PLY (smaller, faster).  Set False for ASCII.
    fallback_to_rgb : bool, default=True
        If tensor IO is unavailable (Open3D < 0.17), map labels → RGB colours
        and still write a valid PLY.

    Raises
    ------
    ImportError
        If Open3D < 0.17 **and** fallback_to_rgb=False.
    """
    filename = Path(filename).with_suffix(".ply")

    # ── 1. Preferred path ── Tensor PointCloud (Open3D ≥ 0.17)
    if hasattr(o3d, "t") and hasattr(o3d.t, "geometry"):
        dev = o3d.core.Device("CPU:0")
        pc_t = o3d.t.geometry.PointCloud(dev)           # tensor API :contentReference[oaicite:0]{index=0}
        pc_t.point["positions"] = o3d.core.Tensor(xyz, o3d.core.float32, dev)
        pc_t.point["labels"]    = o3d.core.Tensor(labels.reshape(-1, 1),
                                                  o3d.core.int32,  dev)  # 自定义属性 :contentReference[oaicite:1]{index=1}
        ok = o3d.t.io.write_point_cloud(                # 原生写入 label 列 
            str(filename), pc_t,
            write_ascii=not binary,
            compressed=False,
            print_progress=True,
        )
        if not ok:
            raise IOError(f"Open3D failed to write {filename}")

    # ── 2. Fallback ── classic geometry API + colour-mapping
    else:
        if not fallback_to_rgb:
            raise ImportError("Your Open3D is too old; upgrade ≥0.17 or enable RGB fallback.")
        import matplotlib as mpl
        cmap  = mpl.colormaps["tab20"]                  # 新 API，避免弃用警告 
        rgb   = cmap(labels / labels.max())[:, :3]

        pc = o3d.geometry.PointCloud()                  # 旧接口写不了自定义列 
        pc.points = o3d.utility.Vector3dVector(xyz)
        pc.colors = o3d.utility.Vector3dVector(rgb)
        o3d.io.write_point_cloud(                       # 仅 xyz+rgb，但各类查看器都能显示 :contentReference[oaicite:5]{index=5}
            str(filename), pc,
            write_ascii=not binary,
            compressed=False,
            print_progress=True,
        )

    print(f"Saved PLY with {xyz.shape[0]} points → {filename}")


import numpy as np
from typing import Tuple

###############################################################################
# Basic seed–geo–vfm fusion                                                  #
###############################################################################
def iou(mask_a: np.ndarray, mask_b: np.ndarray) -> float:
    inter = np.logical_and(mask_a, mask_b).sum()
    if inter == 0:
        return 0.0
    union = mask_a.sum() + mask_b.sum() - inter
    # print(f'**** cal iou: inter: {inter}, union: {union}, iou: {inter / union}, mask_a.sum(): {mask_a.sum()}, mask_b.sum(): {mask_b.sum()}')
    return inter / union
    
def per_class_high_conf_seed(pred_sem: np.ndarray,
                             pred_score: np.ndarray,
                             thresh_cap: float = 0.9,
                             ignore_cls: list = []) -> np.ndarray:
    """Return bool mask of high‑confidence seed points (per‑class median rule)."""
    seed = np.zeros_like(pred_sem, dtype=bool)
    for cls in np.unique(pred_sem):
        if cls in ignore_cls:
            continue
        cls_mask = pred_sem == cls
        if not np.any(cls_mask):
            continue
        # med = np.median(pred_score[cls_mask]) # here mean is better; median -> half of the points must be filtered out
        med = np.mean(pred_score[cls_mask])
        thresh = min(med, thresh_cap)
        seed |= cls_mask & (pred_score > thresh)
        print(f'class: {cls}, med score: {med}, thresh: {thresh}, num of seed: {(cls_mask & (pred_score > thresh)).sum()}/{cls_mask.sum()}')
    return seed


def instance_masks_from_ids(inst_ids: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
    """Return list of unique ids and a mapping id->boolean point mask."""
    uniq = np.unique(inst_ids)
    masks = {}
    for iid in uniq:
        if iid <= 0:
            continue
        masks[iid] = inst_ids == iid
    return uniq, masks


def basic_fuse_seed(pred_inst: np.ndarray,
                    geo_inst: np.ndarray,
                    pred_sem: np.ndarray,
                    iou_thr: float = 0.3,
                    ignore_cls: list = []) -> np.ndarray:
    """Simple fusion: seed from Pred + IoU grow with GEO mask.
    Returns: final_instance_mask (same shape as input).
    """
    N = pred_inst.shape[0]
    final_ins = np.full(N, 0, dtype=int)
    final_sem = pred_sem.copy()
    
    # 1. Seed selection 
    # seed_loc = per_class_high_conf_seed(pred_sem, pred_score, ignore_cls=ignore_cls)
    seed = pred_inst
    # 2. Grow via GEO: if matched, merge; otherwise keep seed
    _, geo_masks = instance_masks_from_ids(geo_inst)    
    # print(f'num of unique high conf seed: {len(np.unique(pred_ins[seed_loc]))-1}')
    for seed_id in np.unique(seed):
        if seed_id == 0: continue
        mask = seed == seed_id
        sem_label = get_sem_label(pred_sem, mask)
        
        best_gid, best_iou = None, 0.0
        for gid, gmask in geo_masks.items():
            iou_val = iou(mask, gmask)
            if iou_val > best_iou:
                best_iou, best_gid = iou_val, gid
                # print(seed_id, best_iou, best_gid)
        if best_gid is not None and best_iou >= iou_thr:
            gmask = geo_masks[best_gid]
            grown_mask = np.logical_or(mask, gmask)
            final_ins[grown_mask] = seed_id
            final_sem[grown_mask] = sem_label
        else:
            final_ins[mask] = seed_id
            final_sem[mask] = sem_label
    return final_ins, final_sem

def fuse_seed_conf_onlyinst(pred_ins, pred_score_mat, 
                           geo_inst, geo_score_mat,
                           iou_thr=0.3, mix_thr=0.25, alpha=0.5, beta=0.5):
    final_mask = np.full_like(pred_ins, 0)
    uniq_pred, pred_masks = instance_masks_from_ids(pred_ins)
    _, geo_masks          = instance_masks_from_ids(geo_inst)

    for sid in uniq_pred:
        if sid == 0:
            continue
        smask = pred_masks[sid]
        best_gid, best_mix = None, 0.0
        for gid, gmask in geo_masks.items():
            iou_val = iou(smask, gmask)
            if iou_val < iou_thr:
                continue
            # 置信度
            conf_pred = pred_score_mat[gmask, sid].mean()
            conf_geo  = geo_score_mat[gmask, gid].mean()
            mix       = iou_val * (alpha*conf_pred + beta*conf_geo)
            if mix > best_mix:
                best_mix, best_gid = mix, gid
        if best_gid is not None and best_mix >= mix_thr:
            smask = np.logical_or(smask, geo_masks[best_gid])
        final_mask[smask] = sid
    return final_mask


import numpy as np
from collections import Counter

def get_sem_label(sem_map, mask):
    """
    从语义预测 sem_map 中，统计 mask 区域内出现最多的标签，作为该 instance 的语义标签。
    """
    labels = sem_map[mask]
    if labels.size == 0:
        return 0
    cnt = Counter(labels.ravel())
    # 排除背景 0，如果都只有 0 则返回 0
    cnt.pop(0, None)
    return cnt.most_common(1)[0][0] if cnt else 0

def fuse_seed_conf(pred_ins, pred_score_mat, 
                           geo_inst, geo_score_mat,
                           pred_sem,
                           iou_thr=0.3, mix_thr=0.25, alpha=0.5, beta=0.5):
    final_ins = np.full_like(pred_ins, 0)
    final_sem = pred_sem.copy()
    uniq_pred, pred_masks = instance_masks_from_ids(pred_ins)
    _, geo_masks          = instance_masks_from_ids(geo_inst)

    for sid in uniq_pred:
        if sid == 0:
            continue
        smask = pred_masks[sid]
        sem_label = get_sem_label(pred_sem, smask)
        
        best_gid, best_mix = None, 0.0
        for gid, gmask in geo_masks.items():
            iou_val = iou(smask, gmask)
            if iou_val < iou_thr:
                continue
            # 置信度
            # print(f'sid: {sid}, gid: {gid}, iou: {iou_val}')
            conf_pred = pred_score_mat[gmask, sid].mean()
            conf_geo  = geo_score_mat[gmask, gid].mean()
            mix       = iou_val * (alpha*conf_pred + beta*conf_geo)
            if mix > best_mix:
                
                best_mix, best_gid = mix, gid
        if best_gid is not None and best_mix >= mix_thr:
            smask = np.logical_or(smask, geo_masks[best_gid])
        final_ins[smask] = sid
        final_sem[smask] = sem_label
    return final_ins, final_sem


start_idx = 7
end_idx = 8
for i in range(start_idx, end_idx):
    point_coor = input_dicts[i]['points'][:, :3]
    point_clamp_polar = clamp_point_polar_np(cartesian2polar(point_coor), point_cloud_range_polar)
    point_clamp_cart = polar2cartesian(point_clamp_polar)
    
    # PL
    PL_sem, PL_ins, PL_sem_score, PL_mask_score = input_dicts[i]['pseudo_label']['pts_semantic_mask'], input_dicts[i]['pseudo_label']['pts_instance_mask'], input_dicts[i]['pseudo_label']['pts_sem_score'], input_dicts[i]['pseudo_label']['pts_mask_score']
    PL_mask_score = PL_mask_score.T
    # for sam mask
    ground_mask = get_ground_mask(point_coor)
    sam_pmask = input_dicts[i]['sam']['sam_pmask'].copy()
    sam_pscore = input_dicts[i]['sam']['sam_pscore'].copy()
    SAM_inst = sam_pmask*~ground_mask
    SAM_score = sam_pscore*~ground_mask
    _, SAM_inst = np.unique(SAM_inst, return_inverse=True)  # re-label instance
    SAM_score = lift_scores_numpy(SAM_score, SAM_inst)      # lift score
    # 1. fuse SAM mask by iou
    # grown_ins_mask = basic_fuse_seed(PL_sem, PL_ins, PL_sem_score, SAM_mask, ignore_cls=[0])
    # 2. fuse SAM mask by confidence    
    # print(f'before grow: ')
    # for cls in np.unique(PL_sem):
    #     print(f'PL_sem {cls}: {np.sum(PL_sem==cls)}')
    # for id in np.unique(PL_ins):
    #     print(f'PL_ins {id}: {np.sum(PL_ins==id)}')
    grown_ins_mask, grown_sem_mask = fuse_seed_conf(PL_ins, PL_mask_score, 
                                    SAM_inst, SAM_score, 
                                    PL_sem,
                                    iou_thr=0.3, mix_thr=0.25, alpha=0.5, beta=0.5)
    # print(f'after grow: ')
    # for cls in np.unique(grown_sem_mask):
    #     print(f'PL_sem {cls}: {np.sum(grown_sem_mask==cls)}')
    # for id in np.unique(grown_ins_mask):
    #     print(f'PL_ins {id}: {np.sum(grown_ins_mask==id)}')
    
    
    # # plot_superpoints_bev_matplotlib(point_clamp_cart, PL_sem*~ground_mask)
    # # plot_superpoints_bev_matplotlib(point_clamp_cart, PL_ins)
    # # plot_superpoints_bev_matplotlib(point_clamp_cart, SAM_mask)
    # # plot_superpoints_bev_matplotlib(point_clamp_cart, grown_mask)
    print(f'num of unique PL_ins (before grow): {len(np.unique(PL_ins))-1}')
    print(f'num of unique grown mask: {len(np.unique(grown_ins_mask))-1}')
    

input_dicts_plrefine = input_dicts.copy()
start_idx = 0
end_idx = len(input_dicts)
for i in range(start_idx, end_idx):
    point_coor = input_dicts[i]['points'][:, :3]
    point_clamp_polar = clamp_point_polar_np(cartesian2polar(point_coor), point_cloud_range_polar)
    point_clamp_cart = polar2cartesian(point_clamp_polar)
    
    # 1. load PL
    PL_sem, PL_ins, PL_sem_score = input_dicts[i]['pseudo_label']['pts_semantic_mask'], input_dicts[i]['pseudo_label']['pts_instance_mask'], input_dicts[i]['pseudo_label']['pts_sem_score']
    # PL_sem, PL_ins, PL_sem_score, PL_mask_score = input_dicts[i]['pseudo_label']['pts_semantic_mask'], input_dicts[i]['pseudo_label']['pts_instance_mask'], input_dicts[i]['pseudo_label']['pts_sem_score'], input_dicts[i]['pseudo_label']['pts_mask_score']
    PL_mask_score = PL_mask_score.T
    
    # 2. process geo mask
    geo_ins, geo_ground = process_RANSAC_HDB(  # -1 for ground, instance id start from 0 (?)
                                    point_coor,
                                    voxel_size=0.05,
                                    dist_thresh=0.20,
                                    ground_mode=None      # None: 不细分; 改成 "cluster" 可细分 driveable/sidewalk
                                )
    geo_ins = geo_ins + 1
    geo_ins_score = lift_scores_numpy((geo_ins!=0).astype(bool), geo_ins) # set to 1 for all instances
    # 3. fuse geo mask
    grown_ins_mask, grown_sem_mask = basic_fuse_seed(PL_ins, geo_ins, PL_sem, iou_thr=0.3, ignore_cls=[0])
    # grown_ins_mask, grown_sem_mask = fuse_seed_conf(PL_ins, PL_mask_score, 
    #                                 geo_ins, geo_ins_score, 
    #                                 PL_sem,
    #                                 iou_thr=0.3, mix_thr=0.25, alpha=0.5, beta=0.5)
    
    # 4. load sam mask
    # geo_ground = get_ground_mask(point_coor)
    sam_pmask = input_dicts[i]['sam']['sam_pmask'].copy()
    sam_pscore = input_dicts[i]['sam']['sam_pscore'].copy()
    SAM_inst = sam_pmask*~geo_ground
    SAM_score = sam_pscore*~geo_ground
    _, SAM_inst = np.unique(SAM_inst, return_inverse=True)  # re-label instance
    SAM_score = lift_scores_numpy(SAM_score, SAM_inst)      # lift score
    
    # # 5.1. fuse SAM mask by iou
    grown_ins_mask, grown_sem_mask = basic_fuse_seed(grown_ins_mask, SAM_inst, grown_sem_mask, ignore_cls=[0])
    # # 5.2. fuse SAM mask by confidence    
    # grown_ins_mask, grown_sem_mask = fuse_seed_conf(PL_ins, PL_mask_score, 
    #                                 SAM_inst, SAM_score, 
    #                                 PL_sem,
    #                                 iou_thr=0.3, mix_thr=0.25, alpha=0.5, beta=0.5)
    
    # # plot_superpoints_bev_matplotlib(point_clamp_cart, PL_sem*~ground_mask)
    # # plot_superpoints_bev_matplotlib(point_clamp_cart, PL_ins)
    # # plot_superpoints_bev_matplotlib(point_clamp_cart, SAM_mask)
    # # plot_superpoints_bev_matplotlib(point_clamp_cart, grown_mask)
    # print(f'num of unique PL_ins (before grow): {len(np.unique(PL_ins))-1}')
    # print(f'num of unique grown mask: {len(np.unique(grown_ins_mask))-1}')
    input_dicts_plrefine[i]['pseudo_label_refine'] = dict()
    input_dicts_plrefine[i]['pseudo_label_refine']['pts_instance_mask'] = grown_ins_mask
    input_dicts_plrefine[i]['pseudo_label_refine']['pts_semantic_mask'] = grown_sem_mask


# PQ eval
gt_labels, seg_preds = [], []
for i, frame in enumerate(input_dicts_plrefine):
    eval_info = dict()
    eval_info['pts_instance_mask'] = frame['pts_instance_mask']    
    eval_info['pts_semantic_mask'] = frame['pts_semantic_mask']
    # print('gt: ', np.unique(frame['pts_semantic_mask']), np.unique(frame['pts_instance_mask']))
            
    cpu_pred_3d = dict()
    cpu_pred_3d['pts_semantic_mask'] = frame['pseudo_label_refine']['pts_semantic_mask']
    cpu_pred_3d['pts_instance_mask'] = frame['pseudo_label_refine']['pts_instance_mask']
    # print('pl: ', np.unique(cpu_pred_3d['pts_semantic_mask']), np.unique(cpu_pred_3d['pts_instance_mask']))
    gt_labels.append(eval_info) 
    seg_preds.append(cpu_pred_3d)
    # print(np.unique(frame['pseudo_label']['pts_semantic_mask']))
    
ret_dict = panoptic_seg_eval(gt_labels, seg_preds, classes_name,
            thing_classes, stuff_classes, include, dataset_type,
            min_num_points, id_offset,
            label2cat, ignore_index, logger)

np.where(geo_ins_score[:, 1]>0)[0].shape

def lift_scores_numpy(sem_score: np.ndarray,
                      inst_mask: np.ndarray,
                      ) -> np.ndarray:
    """
    Args:
        sem_score:   shape (N,), 各元素为语义分数
        inst_mask:   shape (N,), 各元素为 0..K-1 的实例索引
        K:           实例数量

    Returns:
        instance_score_mat: shape (N, K)
    """
    N = sem_score.shape[0]
    K = len(np.unique(inst_mask))
    one_hot = np.zeros((N, K), dtype=sem_score.dtype)
    one_hot[np.arange(N), inst_mask] = 1
    instance_score_mat = one_hot * sem_score[:, None]
    return instance_score_mat

import re
import numpy as np
import torch
from mmcv.transforms import BaseTransform
from mmdet3d.registry import TRANSFORMS
from mmdet3d.datasets.transforms.transforms_3d import Compose
from mmdet3d.structures.points import BasePoints


from tools.projection.pc2img import proj_lidar2img
from tools.projection.img_aug import merge_images_yaw, fit_box_cv, expand_box, crop_box_img, paste_box_img, merge_images_pitch_torch, fit_to_box, draw_dashed_box, merge_images_yaw_torch, divide_point_cloud_yaw, divide_point_cloud_pitch, split_yaw, select_mode_by_ratio, update_inst_img

@TRANSFORMS.register_module(force=True)
class PLRefineAdd_SAM_refine(BaseTransform):
    """
    PL refinement by adding instances from SAM.
    """
    def __init__(self,
                 img_aug: bool = True,
                 stuff_classes: Optional[List[int]] = None,
                 ignore_class: Optional[List[int]] = None,
                 iou_gate_enable: bool = True,
                 iou_gate_thr: float = 0.30,
                 iou_mode: str = "mask",   
                 bev_enable: bool = False,             # 打开/关闭 BEV 记录
                 bev_mode: str = "collect",               # "save" 或 "collect"
                 bev_dir: str = "bev_viz",             # save 模式输出目录
                 bev_range: Optional[Tuple[float,float,float,float]] = None,
                 bev_class_names: Optional[Union[Sequence[str], Dict[int,str]]] = None,
                 bev_sample_bg: int = 200000,
                 bev_point_size_bg: float = 0.2,
                 bev_point_size_fg: float = 1.0,
                 bev_collect_max_points: int = 50000
                 ):
        self.img_aug = img_aug
        self.stuff_classes = stuff_classes
        self.ignore_class = ignore_class
        self.bev_enable = bev_enable
        self.bev_mode = bev_mode
        self.bev_dir = bev_dir
        self.bev_range = bev_range
        self.bev_class_names = bev_class_names
        self.bev_sample_bg = bev_sample_bg
        self.bev_point_size_bg = bev_point_size_bg
        self.bev_point_size_fg = bev_point_size_fg
        self.bev_collect_max_points = bev_collect_max_points
        self.iou_gate_enable = bool(iou_gate_enable)
        self.iou_gate_thr = float(iou_gate_thr)
        self.iou_mode = str(iou_mode)

    def transform(self, input_dict):

        points = input_dict['points']
        pl_psem = input_dict['pseudo_label']['pts_semantic_mask']
        pl_pins = input_dict['pseudo_label']['pts_instance_mask']
        pl_pscore = input_dict['pseudo_label']['pts_sem_score']
        sam_pmask = input_dict['sam']['sam_pmask']
        sam_pscore = input_dict['sam']['sam_pscore']
        ground_mask = get_ground_mask(points)
        SAM_pins = sam_pmask*~ground_mask
        SAM_score = sam_pscore*~ground_mask
        
        # gt mask
        gt_psem = input_dict['pts_semantic_mask']
        gt_pins = input_dict['pts_instance_mask']
        
        assert points.shape[0] == pl_psem.shape[0] == SAM_pins.shape[0]
        assert isinstance(points, np.ndarray), "Points should be a numpy array."

        unique_classes = np.unique(pl_psem)
        refined_points = []
        refined_psem = []
        refined_pins = []
        refined_pscore = []
        refined_sam_pmask = []  # 新增：同步更新SAM mask
        refined_sam_pscore = []  # 新增：同步更新SAM score
        refined_gt_psem = []
        refined_gt_pins = []

        instance_dict = {}
        for inst_id in np.unique(pl_pins):
            mask = pl_pins == inst_id
            sem_label = np.unique(pl_psem[mask])
            inst_summary = summarize_instance_semantics(pl_pins[mask], pl_psem[mask], self.ignore_class)
            dominant_sem = inst_summary['dominant_sem']
            if dominant_sem == self.ignore_class or dominant_sem in self.stuff_classes:
                continue
            # 对stuff class和thing class采用不同的处理逻辑
            if len(sem_label) == 1:
                # 单一语义标签的情况
                score = pl_pscore[mask].mean()
                instance_dict[inst_id] = {
                    'mask': mask,
                    'semantic': sem_label[0],
                    'score': score
                }
            else:
                # 多语义标签的情况，检查是否包含stuff class
                stuff_labels = [label for label in sem_label if label in self.stuff_classes]
                if stuff_labels:
                    # 如果包含stuff class，为每个stuff label创建一个mask
                    for stuff_label in stuff_labels:
                        stuff_mask = mask & (pl_psem == stuff_label)
                        if np.any(stuff_mask):  # 确保mask不为空
                            score = pl_pscore[stuff_mask].mean()
                            instance_dict[f"{inst_id}_{stuff_label}"] = {
                                'mask': stuff_mask,
                                'semantic': stuff_label,
                                'score': score
                            }
                # 如果不包含stuff class且有多个标签，则跳过
                else:
                    continue
                
        avg_score_per_class = {
            cls: pl_pscore[pl_psem == cls].mean() for cls in unique_classes
        }

        # pl_coord = points.copy()
        for inst_id, inst_info in instance_dict.items():
            sem = inst_info['semantic']
            score = inst_info['score']
            mask = inst_info['mask']

            # if sem > 10:
            #     print(sem)
            if score >= avg_score_per_class.get(sem, 1.0) or sem in self.stuff_classes or sem == self.ignore_class:
                # print(sem)
                refined_points.append(points[mask])
                refined_psem.append(pl_psem[mask])
                refined_pins.append(pl_pins[mask])
                refined_pscore.append(pl_pscore[mask])
                # 同步更新SAM mask
                refined_sam_pmask.append(SAM_pins[mask])
                refined_sam_pscore.append(SAM_score[mask])
                refined_gt_psem.append(gt_psem[mask])
                refined_gt_pins.append(gt_pins[mask])
                continue

            # #1. use iou to match SAM instances
            # inst_coords = pl_coord[mask]
            # best_iou, best_sam_id = 0, -1
            # for sam_id in np.unique(sam_pmask):
            #     sam_mask = sam_pmask == sam_id
            #     sam_coords = pl_coord[sam_mask]

            #     inter = np.sum(np.isin(inst_coords.view([('', inst_coords.dtype)]*3), sam_coords.view([('', sam_coords.dtype)]*3)))
            #     union = len(inst_coords) + len(sam_coords) - inter
            #     iou = inter / union if union != 0 else 0
            #     if iou > best_iou:
            #         best_iou, best_sam_id = iou, sam_id
            # if best_sam_id == -1 or best_iou < 0.1:
            #     continue  # 没找到合适匹配
            
            # #2. use dominant SAM instance in this area
            sam_ids_in_mask = SAM_pins[mask]
            if len(sam_ids_in_mask) == 0:
                continue
            values, counts = np.unique(sam_ids_in_mask, return_counts=True)
            best_sam_id = values[np.argmax(counts)]
            
            sam_mask = SAM_pins == best_sam_id
            hole_xyz = points[mask][:, :3]
            sam_xyz  = points[sam_mask][:, :3]
            
            # -------- IoU 门控开始 --------
            if self.iou_gate_enable:
                iou_val = _mask_iou_bool(mask, sam_mask)
                # print(f'inst_id: {inst_id}, sem: {sem}, best_sam_id: {best_sam_id}, iou: {iou_val}')

                # 若 IoU 低于阈值，则跳过本次替换
                if iou_val < self.iou_gate_thr:
                    # 可选：记录调试信息
                    dbg = input_dict.setdefault('debug', {}).setdefault('pl_add_iou_logs', [])
                    dbg.append({
                        "inst_id": str(inst_id),
                        "sem": int(sem),
                        "best_sam_id": int(best_sam_id),
                        "iou": float(iou_val),
                        "mode": self.iou_mode,
                        "thr": float(self.iou_gate_thr),
                        "skipped": True
                    })
                    continue
                else:
                    dbg = input_dict.setdefault('debug', {}).setdefault('pl_add_iou_logs', [])
                    dbg.append({
                        "inst_id": str(inst_id),
                        "sem": int(sem),
                        "best_sam_id": int(best_sam_id),
                        "iou": float(iou_val),
                        "mode": self.iou_mode,
                        "thr": float(self.iou_gate_thr),
                        "skipped": False
                    })
            # -------- IoU 门控结束 --------
            # print(f'inst_id: {inst_id}, sem: {sem}, best_sam_id: {best_sam_id}, iou: {iou_val}')

            # 直接 AABB 平移+缩放
            resized_xyz = _affine_resize_aabb(
                sam_xyz, hole_xyz,
                isotropic=True,            # 建议先用等比例缩放，行为更可预期
                quantile=(0.02, 0.98),     # 抑制 outlier
                scale_clip=(0.7, 1.4),
                max_anisotropy=2.5,
                min_axis=1e-3
            )


            if resized_xyz is not None:
                sam_block = points[sam_mask].copy()
                sam_block[:, :3] = resized_xyz

                refined_points.append(sam_block)
                refined_psem.append(np.full(sam_block.shape[0], sem, dtype=np.int64))
                refined_pins.append(np.full(sam_block.shape[0], inst_id, dtype=np.int64))
                s_val = float(np.mean(sam_pscore[sam_mask])) if np.any(sam_pscore[sam_mask]) else float(pl_pscore[mask].mean())
                refined_pscore.append(np.full(sam_block.shape[0], s_val, dtype=np.float32))
                refined_sam_pmask.append(SAM_pins[sam_mask])
                refined_sam_pscore.append(SAM_score[sam_mask])
                refined_gt_psem.append(gt_psem[sam_mask])  # 仅占位
                refined_gt_pins.append(gt_pins[sam_mask])
            else:
                continue
                # # 回退：用原始 sam_mask（保持你现有逻辑）
                # refined_points.append(points[sam_mask])
                # refined_psem.append(np.full(np.sum(sam_mask), sem, dtype=np.int64))
                # refined_pins.append(np.full(np.sum(sam_mask), inst_id, dtype=np.int64))
                # refined_pscore.append(np.full(np.sum(sam_mask), sam_pscore[sam_mask][0], dtype=np.float32))
                # refined_sam_pmask.append(SAM_pins[sam_mask])
                # refined_sam_pscore.append(SAM_score[sam_mask])
                # refined_gt_psem.append(gt_psem[sam_mask])
                # refined_gt_pins.append(gt_pins[sam_mask])

            # vis
            if self.bev_enable:
                try:
                    if self.bev_mode == "save":
                        paths = save_bev_replacement_triplet(
                            points_all=points,
                            hole_mask=mask,
                            sam_mask=sam_mask,
                            resized_xyz=resized_xyz,
                            out_dir=self.bev_dir,
                            inst_id=inst_id,
                            sem_id=sem,
                            class_names=self.bev_class_names,
                            bev_range=self.bev_range,
                            sample_bg=self.bev_sample_bg,
                            point_size_bg=self.bev_point_size_bg,
                            point_size_fg=self.bev_point_size_fg
                        )
                        # 可选择把路径也带回输出字典
                        bev_record = {"inst_id": inst_id, "sem_id": int(sem), "files": paths}
                    else:  # "collect"
                        bev_record = collect_bev_replacement_triplet_data(
                            points_all=points,
                            hole_mask=mask,
                            sam_mask=sam_mask,
                            resized_xyz=resized_xyz,
                            inst_id=inst_id,
                            sem_id=sem,
                            class_names=self.bev_class_names,
                            bev_range=self.bev_range,
                            sample_bg=min(self.bev_sample_bg, 100000),
                            collect_max_points=self.bev_collect_max_points
                        )
                    # 先把记录临时存到一个列表，函数末尾一并塞进返回 dict
                    if 'bev_logs' not in input_dict:
                        input_dict['bev_logs'] = []
                    input_dict['bev_logs'].append(bev_record)
                except Exception as e:
                    print(f"[bev] record failed for inst {inst_id}: {e}")
                
            if self.img_aug:
                img_list = input_dict['img']
                lidar2img = input_dict['img_metas']['lidar2img']
                ori_shape = input_dict['img_metas']['ori_shape']
                scale = input_dict['img_metas']['scale_factor'][0]

                sam_coord = points[sam_mask][:, :3]
                for i, img in enumerate(img_list):
                    proj_pts, vis_mask = proj_lidar2img(
                        torch.tensor(sam_coord),
                        lidar2img[i],
                        img_size=(ori_shape[1], ori_shape[0]),
                        min_dist=1.0)
                    proj_pts = (proj_pts * scale).numpy()
                    box = fit_box_cv(proj_pts)
                    box = expand_box(box, 10, img.shape[:2])
                    patch = crop_box_img(box, img)
                    input_dict['img'][i] = paste_box_img(box, img, patch)

        # 合并所有精炼后的数据
        dedup_input = deduplicate_points(
            np.concatenate(refined_points),
            {
                'pseudo_label': {
                    'pts_semantic_mask': np.concatenate(refined_psem),
                    'pts_instance_mask': np.concatenate(refined_pins),
                    'pts_sem_score': np.concatenate(refined_pscore),
                },
                'sam': {
                    'sam_pmask': np.concatenate(refined_sam_pmask),
                    'sam_pscore': np.concatenate(refined_sam_pscore)
                },
                'pts_semantic_mask': np.concatenate(refined_gt_psem),
                'pts_instance_mask': np.concatenate(refined_gt_pins)
            }
        )
        
        for key in input_dict:
            if key not in dedup_input:
                dedup_input[key] = input_dict[key]
            if self.bev_enable and key == 'bev_logs':
                dedup_input[key] = input_dict[key]
            
        return dedup_input

def deduplicate_points(points, masks_dict, precision=1e-4):
    rounded = np.round(points[:, :3] / precision).astype(np.int64)
    keys = np.ascontiguousarray(rounded).view(
        np.dtype((np.void, rounded.dtype.itemsize * rounded.shape[1]))
    )
    _, unique_indices = np.unique(keys, return_index=True)
    unique_indices = np.sort(unique_indices)
    
    dedup = {
        'points': points[unique_indices]
    }
    for key, val in masks_dict.items():
        if isinstance(val, np.ndarray):
            dedup[key] = val[unique_indices]
        elif isinstance(val, dict):
            for sub_key, sub_val in val.items():
                dedup.setdefault(key, {})
                dedup[key][sub_key] = sub_val[unique_indices]
    return dedup

import numpy as np

from typing import Optional, Tuple

def _aabb_center_halfaxes(
    xyz: np.ndarray,
    quantile: Optional[Tuple[float, float]] = (0.02, 0.98),
    min_axis: float = 1e-3
) -> Tuple[np.ndarray, np.ndarray]:
    """
    估计轴对齐包围盒（AABB）：返回中心 c(3,) 与半轴 a(3,).
    - quantile: 使用分位数抑制离群点；None 则用 min/max。
    - min_axis: 轴长下限，防止除零。
    """
    assert xyz.ndim == 2 and xyz.shape[1] >= 3 and xyz.shape[0] >= 2
    pts = xyz[:, :3].astype(np.float64)
    if quantile is None:
        lo = pts.min(axis=0)
        hi = pts.max(axis=0)
    else:
        ql, qh = quantile
        lo = np.quantile(pts, ql, axis=0)
        hi = np.quantile(pts, qh, axis=0)
    c = (lo + hi) / 2.0
    a = (hi - lo) / 2.0
    a = np.maximum(a, min_axis)
    return c, a

def _affine_resize_aabb(
    sam_xyz: np.ndarray,
    hole_xyz: np.ndarray,
    isotropic: bool = False,
    quantile: Optional[Tuple[float, float]] = (0.02, 0.98),
    scale_clip: Tuple[float, float] = (0.7, 1.4),
    max_anisotropy: float = 3.0,
    min_axis: float = 1e-3
) -> Optional[np.ndarray]:
    """
    用 AABB 直接做平移+缩放，把 SAM 对齐到 hole 的尺寸与中心。
    - isotropic=True: 用统一缩放因子（保持形状比例），更稳更简单。
    - 返回 resized_xyz (N,3) 或 None（不满足约束时回退）。
    """
    if sam_xyz.shape[0] < 2 or hole_xyz.shape[0] < 2:
        return None

    c_s, a_s = _aabb_center_halfaxes(sam_xyz, quantile, min_axis)
    c_h, a_h = _aabb_center_halfaxes(hole_xyz, quantile, min_axis)

    if isotropic:
        # 统一缩放：用体积比的立方根或三轴比的几何均值
        s_uni = np.cbrt((a_h.prod() / max(a_s.prod(), min_axis**3)))
        s_uni = float(np.clip(s_uni, scale_clip[0], scale_clip[1]))
        s = np.array([s_uni, s_uni, s_uni], dtype=np.float64)
    else:
        s = a_h / np.maximum(a_s, min_axis)
        s = np.clip(s, scale_clip[0], scale_clip[1])

    # 各向异性约束：防止某一轴过度拉伸/压缩
    if (s.max() / max(s.min(), 1e-6)) > max_anisotropy:
        return None

    # 仿射：p' = (p - c_s) * s + c_h
    resized = (sam_xyz - c_s) * s + c_h
    return resized.astype(np.float32)


import numpy as np

def summarize_instance_semantics(pl_pins: np.ndarray,
                                 pl_psem: np.ndarray,
                                 ignore_label: int):
    """
    返回:
      inst_summary: dict[inst_id] = {
          "dominant_sem": int,                 # 该实例内出现最多的语义
          "counts_per_sem": {sem: count, ...}, # 该实例内各语义的点数
          "n_points": int                      # 该实例的总点数
      }
    """
    assert pl_pins.shape[0] == pl_psem.shape[0], "pins 与 psem 长度不一致"


    labels, counts = np.unique(pl_psem, return_counts=True)

    # 可选：过滤 ignore_label
    if ignore_label is not None:
        valid = (labels != ignore_label)
        labels, counts = labels[valid], counts[valid]
        if labels.size == 0:
            # 这个实例里只有 ignore 标签
            inst_summary = {
                "dominant_sem": int(ignore_label),
                "counts_per_sem": {},
                "n_points": int(pl_psem.size)
            }

    # dominant 语义（出现次数最多）
    dominant_sem = int(labels[np.argmax(counts)])

    # 保存该实例的统计
    inst_summary = {
        "dominant_sem": dominant_sem,
        "counts_per_sem": {int(l): int(c) for l, c in zip(labels, counts)},
        "n_points": int(pl_psem.size)
    }

    return inst_summary


import numpy as np
from typing import Optional, Tuple

def _mask_iou_bool(hole_mask: np.ndarray, sam_mask: np.ndarray) -> float:
    """点级布尔掩码 IoU"""
    inter = int(np.count_nonzero(hole_mask & sam_mask))
    union = int(np.count_nonzero(hole_mask | sam_mask))
    return (inter / union) if union > 0 else 0.0

def _bev_voxel_iou(hole_xyz: np.ndarray, sam_xyz: np.ndarray,
                   voxel_size: float = 0.2) -> float:
    """BEV (x,y) 体素 IoU，更稳健"""
    if hole_xyz.size == 0 or sam_xyz.size == 0:
        return 0.0
    hv = np.floor(hole_xyz[:, :2] / voxel_size).astype(np.int64)
    sv = np.floor(sam_xyz[:, :2] / voxel_size).astype(np.int64)
    # 用 void 视图做唯一集合
    def _to_keys(v):
        return np.ascontiguousarray(v).view(
            np.dtype((np.void, v.dtype.itemsize * v.shape[1]))
        )
    hk, sk = _to_keys(hv), _to_keys(sv)
    hset, sset = set(hk), set(sk)
    inter = len(hset & sset)
    union = len(hset | sset)
    return (inter / union) if union > 0 else 0.0


# ======= 放在 PLRefineAdd_GT 类定义之前 =======
import os
import numpy as np
import matplotlib.pyplot as plt
from typing import Optional, Tuple, Union, Dict, Sequence

def _ensure_dir(path: str):
    os.makedirs(path, exist_ok=True)

def _get_class_name(sem_id: int,
                    class_names: Optional[Union[Sequence[str], Dict[int, str]]]) -> str:
    if class_names is None:
        return str(sem_id)
    if isinstance(class_names, dict):
        return class_names.get(int(sem_id), str(sem_id))
    if 0 <= int(sem_id) < len(class_names):
        return str(class_names[int(sem_id)])
    return str(sem_id)

def _xy_from_points(points: np.ndarray) -> np.ndarray:
    assert points.ndim == 2 and points.shape[1] >= 2, "points must be (N,C>=2)"
    return points[:, :2].astype(np.float32)

def _aabb_xy(xy: np.ndarray) -> Tuple[float, float, float, float]:
    return float(np.min(xy[:,0])), float(np.max(xy[:,0])), float(np.min(xy[:,1])), float(np.max(xy[:,1]))

def _compute_bev_extent(xy_list, bev_range: Optional[Tuple[float,float,float,float]], margin: float=2.0):
    if bev_range is not None:
        return bev_range
    xs, ys = [], []
    for xy in xy_list:
        if xy is None or len(xy) == 0: 
            continue
        xs.append(xy[:,0]); ys.append(xy[:,1])
    if not xs:
        return (-10, 10, -10, 10)
    xmin, xmax = float(np.min(np.concatenate(xs))), float(np.max(np.concatenate(xs)))
    ymin, ymax = float(np.min(np.concatenate(ys))), float(np.max(np.concatenate(ys)))
    return (xmin - margin, xmax + margin, ymin - margin, ymax + margin)

def _draw_rect_ax(ax, xy: np.ndarray, color: str, lw: float=1.0):
    xmin, xmax, ymin, ymax = _aabb_xy(xy)
    ax.add_patch(plt.Rectangle((xmin, ymin), xmax-xmin, ymax-ymin,
                               fill=False, edgecolor=color, linewidth=lw))

def _scatter_bev(ax, xy_bg: np.ndarray, xy_fg: Optional[np.ndarray],
                 fg_color: str, bg_color: str='0.75', s_bg: float=0.2, s_fg: float=1.0,
                 draw_rect_fg: bool=True, rect_color: Optional[str]=None, title: Optional[str]=None):
    if xy_bg is not None and len(xy_bg) > 0:
        ax.scatter(xy_bg[:,0], xy_bg[:,1], s=s_bg, c=bg_color, linewidths=0)
    if xy_fg is not None and len(xy_fg) > 0:
        ax.scatter(xy_fg[:,0], xy_fg[:,1], s=s_fg, c=fg_color, linewidths=0)
        if draw_rect_fg:
            _draw_rect_ax(ax, xy_fg, color=(rect_color or fg_color), lw=1.2)
    ax.set_aspect('equal', adjustable='box')
    ax.set_xlabel('X (m)'); ax.set_ylabel('Y (m)')
    if title: ax.set_title(title)
    ax.grid(False)

def save_bev_replacement_triplet(
    points_all: np.ndarray,           # (N,C>=2) 全局点云
    hole_mask: np.ndarray,            # (N,) bool，原实例（将被删除/替换）
    sam_mask: np.ndarray,             # (N,) bool，选择的 SAM 实例（替换前）
    resized_xyz: Optional[np.ndarray],# (M,3) 替换后的点（变换后 SAM）
    out_dir: str,
    inst_id: Union[int,str],
    sem_id: int,
    class_names: Optional[Union[Sequence[str], Dict[int,str]]] = None,
    bev_range: Optional[Tuple[float,float,float,float]] = None,
    sample_bg: int = 200000,
    point_size_bg: float = 0.2,
    point_size_fg: float = 1.0
) -> Tuple[str, str, Optional[str]]:
    """
    保存 3 张图：1 删除(hole, 红)；2 选择的 SAM(蓝)；3 替换后(绿)。
    返回 (path1, path2, path3 或 None)。
    """
    _ensure_dir(out_dir)
    xy_all = _xy_from_points(points_all)
    fg_union = hole_mask | sam_mask
    bg_idx = np.flatnonzero(~fg_union)
    if bg_idx.size > sample_bg:
        bg_idx = np.random.choice(bg_idx, sample_bg, replace=False)
    xy_bg   = xy_all[bg_idx] if bg_idx.size > 0 else None
    xy_hole = xy_all[hole_mask] if np.any(hole_mask) else None
    xy_sB   = xy_all[sam_mask] if np.any(sam_mask) else None
    xy_sA   = _xy_from_points(resized_xyz) if (resized_xyz is not None and len(resized_xyz) > 0) else None
    extent  = _compute_bev_extent([xy_bg, xy_hole, xy_sB, xy_sA], bev_range, margin=2.0)
    cls_name= _get_class_name(sem_id, class_names)
    base = f"inst{inst_id}_cls{sem_id}_{cls_name}"

    # 1) hole
    fig, ax = plt.subplots(figsize=(6,6), dpi=140)
    _scatter_bev(ax, xy_bg, xy_hole, fg_color='#d62728',
                 s_bg=point_size_bg, s_fg=point_size_fg, rect_color='#b2182b',
                 title=f"Deleted (hole) — inst {inst_id}, cls {sem_id} ({cls_name})")
    ax.set_xlim(extent[0], extent[1]); ax.set_ylim(extent[2], extent[3])
    fig.tight_layout()
    f1 = os.path.join(out_dir, f"{base}_1_deleted.png"); fig.savefig(f1); plt.close(fig)

    # 2) SAM before
    fig, ax = plt.subplots(figsize=(6,6), dpi=140)
    _scatter_bev(ax, xy_bg, xy_sB, fg_color='#1f77b4',
                 s_bg=point_size_bg, s_fg=point_size_fg, rect_color='#084594',
                 title=f"SAM chosen (before) — inst {inst_id}, cls {sem_id} ({cls_name})")
    ax.set_xlim(extent[0], extent[1]); ax.set_ylim(extent[2], extent[3])
    fig.tight_layout()
    f2 = os.path.join(out_dir, f"{base}_2_sam_before.png"); fig.savefig(f2); plt.close(fig)

    # 3) after
    if xy_sA is not None:
        fig, ax = plt.subplots(figsize=(6,6), dpi=140)
        _scatter_bev(ax, xy_bg, xy_sA, fg_color='#2ca02c',
                     s_bg=point_size_bg, s_fg=point_size_fg, rect_color='#006d2c',
                     title=f"Replaced (after) — inst {inst_id}, cls {sem_id} ({cls_name})")
        ax.set_xlim(extent[0], extent[1]); ax.set_ylim(extent[2], extent[3])
        fig.tight_layout()
        f3 = os.path.join(out_dir, f"{base}_3_after.png"); fig.savefig(f3); plt.close(fig)
    else:
        f3 = None
    return f1, f2, f3

def collect_bev_replacement_triplet_data(
    points_all: np.ndarray,
    hole_mask: np.ndarray,
    sam_mask: np.ndarray,
    resized_xyz: Optional[np.ndarray],
    inst_id: Union[int,str],
    sem_id: int,
    class_names: Optional[Union[Sequence[str], Dict[int,str]]] = None,
    bev_range: Optional[Tuple[float,float,float,float]] = None,
    sample_bg: int = 100000,         # 为了字典大小，默认缩小一点
    collect_max_points: int = 50000  # 前景最多收这么多点，避免爆内存
) -> Dict:
    """
    不落盘，返回一个字典（下游可自行可视化）。
    为控制体量，会下采样 background 和前景。
    """
    xy_all = _xy_from_points(points_all)
    fg_union = hole_mask | sam_mask
    bg_idx = np.flatnonzero(~fg_union)
    if bg_idx.size > sample_bg:
        bg_idx = np.random.choice(bg_idx, sample_bg, replace=False)
    xy_bg   = xy_all[bg_idx] if bg_idx.size > 0 else None
    xy_hole = xy_all[hole_mask] if np.any(hole_mask) else None
    xy_sB   = xy_all[sam_mask] if np.any(sam_mask) else None
    xy_sA   = _xy_from_points(resized_xyz) if (resized_xyz is not None and len(resized_xyz) > 0) else None

    # 控制前景点数
    def _limit(xy):
        if xy is None or len(xy) == 0: return xy
        if len(xy) > collect_max_points:
            idx = np.random.choice(len(xy), collect_max_points, replace=False)
            return xy[idx]
        return xy

    xy_hole = _limit(xy_hole)
    xy_sB   = _limit(xy_sB)
    xy_sA   = _limit(xy_sA)
    extent  = _compute_bev_extent([xy_bg, xy_hole, xy_sB, xy_sA], bev_range, margin=2.0)
    return {
        "inst_id": inst_id,
        "sem_id": int(sem_id),
        "class_name": _get_class_name(sem_id, class_names),
        "bev_range": extent,
        "xy_bg": xy_bg,              # (K,2) or None
        "xy_deleted": xy_hole,       # (M1,2) or None
        "xy_sam_before": xy_sB,      # (M2,2) or None
        "xy_after": xy_sA            # (M3,2) or None
    }


import numpy as np
import matplotlib.pyplot as plt

# 为 save 模式显示图片
from PIL import Image
from pathlib import Path

# 可选：交互滑块
try:
    import ipywidgets as widgets
    _HAS_WIDGETS = True
except Exception:
    _HAS_WIDGETS = False


def _scatter_bev(ax, xy_bg, xy_fg, *,
                 fg_color='#d62728',           # 前景颜色
                 bg_color='0.75',              # 背景灰
                 s_bg=0.2, s_fg=1.0,
                 draw_rect=True, rect_color=None,
                 title=None, extent=None):
    """在 ax 上画一幅 BEV 散点图（x-y），前景高亮，背景灰。"""
    if xy_bg is not None and len(xy_bg) > 0:
        ax.scatter(xy_bg[:,0], xy_bg[:,1], s=s_bg, c=bg_color, linewidths=0)
    if xy_fg is not None and len(xy_fg) > 0:
        ax.scatter(xy_fg[:,0], xy_fg[:,1], s=s_fg, c=fg_color, linewidths=0)
        if draw_rect:
            xmin, xmax = float(np.min(xy_fg[:,0])), float(np.max(xy_fg[:,0]))
            ymin, ymax = float(np.min(xy_fg[:,1])), float(np.max(xy_fg[:,1]))
            ax.add_patch(plt.Rectangle((xmin, ymin), xmax-xmin, ymax-ymin,
                                       fill=False, edgecolor=(rect_color or fg_color), linewidth=1.2))
    ax.set_aspect('equal', adjustable='box')
    ax.grid(False)
    ax.set_xlabel('X (m)'); ax.set_ylabel('Y (m)')
    if title:
        ax.set_title(title)
    if extent is not None:
        ax.set_xlim(extent[0], extent[1]); ax.set_ylim(extent[2], extent[3])


def _compute_extent(xys, default_margin=2.0):
    """根据若干组 xy 自动计算视野范围 (xmin, xmax, ymin, ymax)。"""
    xs, ys = [], []
    for xy in xys:
        if xy is None or len(xy) == 0: 
            continue
        xs.append(xy[:,0]); ys.append(xy[:,1])
    if not xs:
        return (-10, 10, -10, 10)
    xmin, xmax = float(np.min(np.concatenate(xs))), float(np.max(np.concatenate(xs)))
    ymin, ymax = float(np.min(np.concatenate(ys))), float(np.max(np.concatenate(ys)))
    return (xmin - default_margin, xmax + default_margin,
            ymin - default_margin, ymax + default_margin)


def plot_bev_triplet_from_log(log, *, figsize=(16, 5), point_size_bg=0.2, point_size_fg=1.0):
    """
    根据一条 bev_log 绘制三联图。
    - save 模式：读取 log['files'] 的三张 PNG 并排显示；
    - collect 模式：使用 xy_* 数组绘制三张 BEV 散点图。
    """
    # ===== 情况 A：save 模式（落盘了 PNG） =====
    if isinstance(log, dict) and 'files' in log:
        files = log['files']
        paths = [Path(p) for p in files if p]
        n = len(paths)
        if n == 0:
            print("No files recorded in this log.")
            return
        fig, axes = plt.subplots(1, n, figsize=(figsize[0], figsize[1]))
        if n == 1:
            axes = [axes]
        titles = ["1) Deleted (hole)", "2) SAM chosen (before)", "3) Replaced (after)"]
        for ax, p, t in zip(axes, paths, titles[:n]):
            if p.exists():
                im = Image.open(p)
                ax.imshow(im)
                ax.set_title(f"{t}\n{p.name}")
                ax.axis('off')
            else:
                ax.text(0.5, 0.5, f"Missing file:\n{p}", ha='center', va='center')
                ax.axis('off')
        plt.tight_layout()
        plt.show()
        return

    # ===== 情况 B：collect 模式（内存中的 xy_*） =====
    # 读取字段（你的 collect_bev_replacement_triplet_data 输出结构）
    xy_bg        = log.get('xy_bg', None)
    xy_deleted   = log.get('xy_deleted', None)
    xy_sam_before= log.get('xy_sam_before', None)
    xy_after     = log.get('xy_after', None)
    extent       = log.get('bev_range', None)
    if extent is None:
        extent = _compute_extent([xy_bg, xy_deleted, xy_sam_before, xy_after])

    inst_id = log.get('inst_id', '?')
    sem_id  = log.get('sem_id', '?')
    clsname = log.get('class_name', str(sem_id))

    fig, axes = plt.subplots(1, 3, figsize=figsize)

    _scatter_bev(axes[0], xy_bg, xy_deleted,
                 fg_color='#d62728', rect_color='#b2182b',
                 s_bg=point_size_bg, s_fg=point_size_fg,
                 title=f"1) Deleted (hole)\ninst {inst_id}, cls {sem_id} ({clsname})",
                 extent=extent)

    _scatter_bev(axes[1], xy_bg, xy_sam_before,
                 fg_color='#1f77b4', rect_color='#084594',
                 s_bg=point_size_bg, s_fg=point_size_fg,
                 title=f"2) SAM chosen (before)",
                 extent=extent)

    _scatter_bev(axes[2], xy_bg, xy_after,
                 fg_color='#2ca02c', rect_color='#006d2c',
                 s_bg=point_size_bg, s_fg=point_size_fg,
                 title=f"3) Replaced (after)",
                 extent=extent)

    plt.tight_layout()
    plt.show()


def browse_bev_logs(bev_logs, idx=None, limit=None, **kwargs):
    """
    - 交互浏览：如果安装了 ipywidgets，则用滑块浏览；
    - 否则：绘制第 idx 条（默认第 0 条），或前 limit 条。
    kwargs 会传到 plot_bev_triplet_from_log。
    """
    if not isinstance(bev_logs, (list, tuple)) or len(bev_logs) == 0:
        print("bev_logs is empty.")
        return

    if _HAS_WIDGETS and idx is None and limit is None:
        slider = widgets.IntSlider(value=0, min=0, max=len(bev_logs)-1, step=1,
                                   description='log idx:', continuous_update=False)
        out = widgets.interactive_output(lambda i: plot_bev_triplet_from_log(bev_logs[i], **kwargs),
                                         {'i': slider})
        display(slider, out)
    else:
        if idx is not None:
            plot_bev_triplet_from_log(bev_logs[idx], **kwargs)
        else:
            n = len(bev_logs) if limit is None else min(limit, len(bev_logs))
            for i in range(n):
                print(f"=== log {i}/{len(bev_logs)-1} ===")
                plot_bev_triplet_from_log(bev_logs[i], **kwargs)


input_dicts_plrefine = input_dicts.copy()
plrefine = PLRefineAdd_SAM_refine(
        img_aug=False,  # 这里设置为False因为我们没有提供图像
        stuff_classes=[11,12,13,14,15,16],
        ignore_class=0,
        iou_gate_thr=0.30,
        bev_enable=True,
        bev_mode="collect"
    )

for i in range(len(input_dicts)):
    input_dicts_plrefine[i] = plrefine.transform(input_dicts[i])



bev_logs = input_dicts_plrefine[6]['bev_logs']
browse_bev_logs(bev_logs)




for i in range(len(input_dicts)):
    if 'bev_logs' in input_dicts_plrefine[i]:
        print(i)







from evaluation.functional.panoptic_seg_eval import EvalPanoptic
import numpy as np

# 设置评估参数
classes_name = ['noise', 'barrier', 'bicycle', 'bus', 'car', 'construction_vehicle', 'motorcycle', 'pedestrian', 'traffic_cone', 'trailer', 'truck', 'driveable_surface', 'other_flat', 'sidewalk', 'terrain', 'manmade', 'vegetation']
thing_classes = ['barrier', 'bicycle', 'bus', 'car', 'construction_vehicle', 'motorcycle', 'pedestrian', 'traffic_cone', 'trailer', 'truck']
stuff_classes = ['driveable_surface', 'other_flat', 'sidewalk', 'terrain', 'manmade', 'vegetation']
include = [1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16]
min_num_points = 15  # 最小点数阈值
id_offset = 1000  # 实例ID偏移量
ignore_index = 0  # 忽略的类别
dataset_type = 'nuscenes'
logger = None

gt_labels, seg_preds = [], []
for i, frame in enumerate(input_dicts):
    eval_info = dict()
    eval_info['pts_instance_mask'] = frame['pts_instance_mask']    
    eval_info['pts_semantic_mask'] = frame['pts_semantic_mask']
    # print('gt: ', np.unique(frame['pts_semantic_mask']), np.unique(frame['pts_instance_mask']))
            
    cpu_pred_3d = dict()
    cpu_pred_3d['pts_semantic_mask'] = frame['pseudo_label']['pts_semantic_mask']
    cpu_pred_3d['pts_instance_mask'] = frame['pseudo_label']['pts_instance_mask']
    # print('pl: ', np.unique(cpu_pred_3d['pts_semantic_mask']), np.unique(cpu_pred_3d['pts_instance_mask']))
    gt_labels.append(eval_info) 
    seg_preds.append(cpu_pred_3d)
    # print(np.unique(frame['pseudo_label']['pts_semantic_mask']))
    
ret_dict = panoptic_seg_eval(gt_labels, seg_preds, classes_name,
            thing_classes, stuff_classes, include, dataset_type,
            min_num_points, id_offset,
            label2cat, ignore_index, logger)

len(res_list)

# load PL pred from assigned folder

# GTgrowsi
pred_PL_path = os.path.join(proj_path, f'output/UDA_debug/{SHIFT_TYPE}')
assert os.path.exists(pred_PL_path), f"PL pred path {pred_PL_path} does not exist"



input_dicts_predPL = []
gt_labels, seg_preds = [], []

# # V1: index from lidar token
# for i,frame in enumerate(res_list[:5]):
#     print(i)
#     pl_data = dict()
#     pl_filename = frame['sample_token']
#     for n in ['gt_sem', 'gt_inst', 'pl_sem', 'pl_inst', 'pl_score', 'point']:
#         d = np.load(os.path.join(pred_PL_path, n, pl_filename + '.npy'))
#         pl_data[n] = d
#     input_dicts_predPL.append(pl_data)

# V2: walk through all files in pred_PL_path
for file in os.listdir(os.path.join(pred_PL_path, 'gt_sem')):
    if file.endswith('.npy'):
        pl_data = dict()
        pl_filename = file.split('.')[0]
        for n in ['gt_sem', 'gt_inst', 'pl_sem', 'pl_inst', 'pl_score', 'point']:
            d = np.load(os.path.join(pred_PL_path, n, pl_filename + '.npy'))
            pl_data[n] = d
        # assert (pl_data['gt_sem'] == pl_data['pl_sem']).all()
        input_dicts_predPL.append(pl_data)
        
    # pack for eval
    eval_info = dict()
    eval_info['pts_semantic_mask'] = pl_data['gt_sem']   
    # eval_info['pts_semantic_mask'] = pl_data['gt_inst']
    gt_inst = pl_data['gt_inst']
    _, gt_inst_inv = np.unique(gt_inst, return_inverse=True)
    eval_info['pts_instance_mask'] = gt_inst_inv
    # print('gt: ', np.unique(pl_data['gt_sem']), np.unique(pl_data['gt_inst']))
            
    cpu_pred_3d = dict()
    cpu_pred_3d['pts_semantic_mask'] = pl_data['pl_sem']
    # cpu_pred_3d['pts_instance_mask'] = pl_data['pl_inst']
    pl_inst = pl_data['pl_inst']
    _, pl_inst_inv = np.unique(pl_inst, return_inverse=True)
    cpu_pred_3d['pts_instance_mask'] = pl_inst_inv
    assert pl_inst_inv.shape[0] == pl_inst.shape[0]
    assert len(np.unique(pl_inst_inv)) == len(np.unique(pl_inst))
    # print('pl: ', np.unique(pl_data['pl_sem']), np.unique(pl_data['pl_inst']))
    gt_labels.append(eval_info) 
    seg_preds.append(cpu_pred_3d)
    # print(np.unique(frame['pseudo_label']['pts_semantic_mask']))
    
ret_dict = panoptic_seg_eval(gt_labels, seg_preds, classes_name,
            thing_classes, stuff_classes, include, dataset_type,
            min_num_points, id_offset,
            label2cat, ignore_index, logger)



